2025-06-15 21:31:59,540 INFO     35 task_executor_4223ea524b83_0 log path: /ragflow/logs/task_executor_4223ea524b83_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-15 21:31:59,541 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-15 21:31:59,545 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-15 21:31:59,549 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-15 21:31:59,559 INFO     35 GET http://es01:9200/ [status:200 duration:0.004s]
2025-06-15 21:31:59,563 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.003s]
2025-06-15 21:31:59,566 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-06-15 21:31:59,580 WARNING  35 Load term.freq FAIL!
2025-06-15 21:31:59,587 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-15 21:31:59,595 WARNING  35 Load term.freq FAIL!
2025-06-15 21:31:59,599 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-15 21:31:59,600 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-06-15 21:31:59,603 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-06-15 21:31:59,618 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:31:59.615+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-15 21:31:59,621 INFO     35 task_executor_810155486def_0 expired, removed
2025-06-15 21:31:59,624 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-15 21:32:04,751 INFO     35 handle_task begin for task {"id": "1ddc213449ed11f0ad7002420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749994320575, "task_type": ""}
2025-06-15 21:32:05,073 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-15 21:32:05,354 INFO     35 PyTorch version 2.6.0 available.
2025-06-15 21:32:06,876 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-15 21:32:07,070 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-15 21:32:10,740 INFO     35 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.014s]
2025-06-15 21:32:11,079 INFO     35 From minio(0.33744462579488754) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-15 21:32:11,109 INFO     35 set_progress(1ddc213449ed11f0ad7002420ae90706), progress: 0.1, progress_msg: 21:32:11 Page(1~3): Start to parse.
2025-06-15 21:32:11,110 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-15 21:32:11,130 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-15 21:32:11,157 INFO     35 set_progress(1ddc213449ed11f0ad7002420ae90706), progress: -1, progress_msg: 21:32:11 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named ditod
2025-06-15 21:32:11,158 ERROR    35 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-15 21:32:11,188 INFO     35 set_progress(1ddc213449ed11f0ad7002420ae90706), progress: -1, progress_msg: 21:32:11 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-15 21:32:11,188 ERROR    35 handle_task got exception for task {"id": "1ddc213449ed11f0ad7002420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749994320575, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-15 21:32:29,626 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:32:29.623+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:32:59,635 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:32:59.633+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:33:29,644 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:33:29.642+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:33:59,651 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:33:59.649+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:34:29,658 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:34:29.656+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:34:59,664 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:34:59.662+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:35:29,672 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:35:29.670+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:35:59,678 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:35:59.676+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:36:29,684 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:36:29.682+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:36:59,691 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:36:59.689+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:37:29,698 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:37:29.696+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:37:59,706 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:37:59.703+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:38:29,713 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:38:29.710+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:38:59,719 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:38:59.717+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:39:29,727 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:39:29.725+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:39:59,733 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:39:59.731+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:40:29,740 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:40:29.738+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:40:59,748 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:40:59.746+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:41:29,758 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:41:29.756+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:41:59,765 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:41:59.763+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:42:29,772 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:42:29.770+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:42:59,780 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:42:59.777+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:43:29,787 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:43:29.785+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:43:59,794 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:43:59.792+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:44:29,819 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:44:29.816+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:44:59,828 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:44:59.826+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:45:29,835 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:45:29.833+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:45:59,842 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:45:59.840+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:46:29,850 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:46:29.847+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:46:59,857 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:46:59.855+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:47:29,864 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:47:29.862+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:47:59,871 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:47:59.869+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:48:29,879 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:48:29.876+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:48:59,886 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:48:59.884+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:49:29,894 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:49:29.892+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:49:59,901 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:49:59.899+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:50:29,908 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:50:29.906+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:50:59,915 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:50:59.913+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:51:29,923 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:51:29.920+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:51:59,931 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:51:59.929+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:52:29,939 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:52:29.936+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:52:59,945 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:52:59.943+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:53:29,953 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:53:29.950+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:53:59,963 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:53:59.959+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:54:29,970 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:54:29.968+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:54:59,977 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:54:59.975+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:55:29,984 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:55:29.982+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:55:59,992 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:55:59.990+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:56:29,999 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:56:29.997+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:57:00,007 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:57:00.005+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-15 21:57:30,014 INFO     35 task_executor_4223ea524b83_0 reported heartbeat: {"name": "task_executor_4223ea524b83_0", "now": "2025-06-15T21:57:30.012+08:00", "boot_at": "2025-06-15T21:31:59.534+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
