2025-06-13 20:38:10,839 INFO     35 task_executor_e800d7e53083_0 log path: /ragflow/logs/task_executor_e800d7e53083_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-13 20:38:10,840 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-13 20:38:10,843 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-13 20:38:10,847 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-13 20:38:10,858 INFO     35 GET http://es01:9200/ [status:200 duration:0.006s]
2025-06-13 20:38:10,862 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.003s]
2025-06-13 20:38:10,865 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-06-13 20:38:10,883 WARNING  35 Load term.freq FAIL!
2025-06-13 20:38:10,892 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-13 20:38:10,903 WARNING  35 Load term.freq FAIL!
2025-06-13 20:38:10,909 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-13 20:38:10,909 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-06-13 20:38:10,912 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-06-13 20:38:10,921 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:38:10.919+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 1, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:38:10,924 INFO     35 task_executor_ed42cbd4b770_0 expired, removed
2025-06-13 20:38:10,928 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-13 20:38:10,993 INFO     35 handle_task begin for task {"id": "42a8835e485311f0911902420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749818288647, "task_type": ""}
2025-06-13 20:38:11,120 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:38:11,405 INFO     35 PyTorch version 2.6.0 available.
2025-06-13 20:38:12,943 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:38:13,136 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:38:16,007 INFO     35 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.008s]
2025-06-13 20:38:16,097 INFO     35 From minio(0.08887621015310287) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-13 20:38:16,123 INFO     35 set_progress(42a8835e485311f0911902420ae90706), progress: 0.1, progress_msg: 20:38:16 Page(1~3): Start to parse.
2025-06-13 20:38:16,124 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-13 20:38:16,144 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-13 20:38:16,219 INFO     35 set_progress(42a8835e485311f0911902420ae90706), progress: -1, progress_msg: 20:38:16 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named detectron2
2025-06-13 20:38:16,219 ERROR    35 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 20:38:16,243 INFO     35 set_progress(42a8835e485311f0911902420ae90706), progress: -1, progress_msg: 20:38:16 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 20:38:16,244 ERROR    35 handle_task got exception for task {"id": "42a8835e485311f0911902420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749818288647, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 20:38:40,930 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:38:40.928+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:39:10,939 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:39:10.937+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:39:40,947 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:39:40.945+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:40:10,956 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:40:10.954+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:40:40,968 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:40:40.966+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:41:10,975 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:41:10.973+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:41:40,981 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:41:40.980+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:42:10,989 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:42:10.986+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:42:40,996 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:42:40.994+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:43:11,002 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:43:11.000+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:43:41,007 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:43:41.005+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:44:11,014 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:44:11.012+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:44:41,020 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:44:41.018+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:45:11,026 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:45:11.025+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:45:41,033 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:45:41.031+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:46:11,037 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:46:11.036+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:46:41,043 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:46:41.041+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:47:11,050 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:47:11.048+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:47:41,055 INFO     35 task_executor_e800d7e53083_0 reported heartbeat: {"name": "task_executor_e800d7e53083_0", "now": "2025-06-13T20:47:41.053+08:00", "boot_at": "2025-06-13T20:38:10.832+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
