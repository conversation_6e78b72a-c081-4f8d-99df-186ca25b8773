2025-06-13 20:14:25,545 INFO     36 task_executor_ed42cbd4b770_0 log path: /ragflow/logs/task_executor_ed42cbd4b770_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-13 20:14:25,546 INFO     36 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-13 20:14:25,556 INFO     36 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-13 20:14:25,563 INFO     36 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-13 20:14:25,580 INFO     36 GET http://es01:9200/ [status:200 duration:0.006s]
2025-06-13 20:14:25,584 INFO     36 HEAD http://es01:9200/ [status:200 duration:0.003s]
2025-06-13 20:14:25,594 INFO     36 Elasticsearch http://es01:9200 is healthy.
2025-06-13 20:14:25,619 WARNING  36 Load term.freq FAIL!
2025-06-13 20:14:25,631 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-06-13 20:14:25,639 WARNING  36 Load term.freq FAIL!
2025-06-13 20:14:25,649 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-06-13 20:14:25,655 INFO     36 MAX_CONTENT_LENGTH: 134217728
2025-06-13 20:14:25,668 INFO     36 MAX_FILE_COUNT_PER_USER: 0
2025-06-13 20:14:25,692 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:14:25.691+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:14:25,698 INFO     36 task_executor_3064955ebe4c_0 expired, removed
2025-06-13 20:14:25,707 WARNING  36 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-13 20:14:55,709 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:14:55.707+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:15:25,717 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:15:25.715+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:15:55,725 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:15:55.723+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:16:25,734 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:16:25.731+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:16:55,746 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:16:55.744+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:17:25,754 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:17:25.752+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 20:17:52,052 INFO     36 handle_task begin for task {"id": "6d7726a6485011f0b34702420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749817071983, "task_type": ""}
2025-06-13 20:17:52,201 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:17:52,591 INFO     36 PyTorch version 2.6.0 available.
2025-06-13 20:17:54,664 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:17:54,921 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 20:17:59,627 INFO     36 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.009s]
2025-06-13 20:17:59,646 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:17:59.642+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 1, "lag": 0, "done": 0, "failed": 0, "current": {"6d7726a6485011f0b34702420ae90706": {"id": "6d7726a6485011f0b34702420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749817071983, "task_type": ""}}}
2025-06-13 20:17:59,981 INFO     36 From minio(0.35095398128032684) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-13 20:18:00,008 INFO     36 set_progress(6d7726a6485011f0b34702420ae90706), progress: 0.1, progress_msg: 20:17:59 Page(1~3): Start to parse.
2025-06-13 20:18:00,009 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-13 20:18:00,028 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-13 20:18:00,051 INFO     36 set_progress(6d7726a6485011f0b34702420ae90706), progress: -1, progress_msg: 20:18:00 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named ditod
2025-06-13 20:18:00,051 ERROR    36 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-13 20:18:00,080 INFO     36 set_progress(6d7726a6485011f0b34702420ae90706), progress: -1, progress_msg: 20:18:00 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-13 20:18:00,081 ERROR    36 handle_task got exception for task {"id": "6d7726a6485011f0b34702420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749817071983, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-13 20:18:29,655 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:18:29.653+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:18:59,662 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:18:59.660+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:19:29,670 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:19:29.667+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:19:59,677 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:19:59.675+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:20:29,683 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:20:29.682+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:20:59,689 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:20:59.688+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:21:29,696 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:21:29.694+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:21:59,706 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:21:59.704+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:22:29,712 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:22:29.710+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:22:59,718 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:22:59.716+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:23:29,729 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:23:29.726+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:23:59,736 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:23:59.734+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:24:29,742 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:24:29.740+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:24:59,749 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:24:59.747+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:25:29,756 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:25:29.754+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:25:59,764 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:25:59.760+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:26:29,773 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:26:29.770+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:26:59,780 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:26:59.778+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:27:29,788 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:27:29.786+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:27:59,794 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:27:59.792+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:28:29,801 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:28:29.799+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:28:59,808 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:28:59.806+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:29:29,815 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:29:29.812+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:29:59,822 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:29:59.820+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:30:29,829 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:30:29.827+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:30:59,836 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:30:59.834+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:31:29,843 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:31:29.841+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:31:59,850 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:31:59.848+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:32:29,856 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:32:29.854+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:32:59,863 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:32:59.861+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:33:29,869 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:33:29.867+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 20:33:59,876 INFO     36 task_executor_ed42cbd4b770_0 reported heartbeat: {"name": "task_executor_ed42cbd4b770_0", "now": "2025-06-13T20:33:59.874+08:00", "boot_at": "2025-06-13T20:14:25.513+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
