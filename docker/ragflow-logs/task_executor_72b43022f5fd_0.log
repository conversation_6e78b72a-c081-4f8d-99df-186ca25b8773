2025-06-16 22:11:43,512 INFO     35 task_executor_72b43022f5fd_0 log path: /ragflow/logs/task_executor_72b43022f5fd_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-16 22:11:43,513 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-16 22:11:43,516 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-16 22:11:43,519 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-16 22:11:43,543 INFO     35 GET http://es01:9200/ [status:200 duration:0.018s]
2025-06-16 22:11:43,549 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.005s]
2025-06-16 22:11:43,550 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-06-16 22:11:43,562 WARNING  35 Load term.freq FAIL!
2025-06-16 22:11:43,571 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-16 22:11:43,580 WARNING  35 Load term.freq FAIL!
2025-06-16 22:11:43,584 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-16 22:11:43,585 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-06-16 22:11:43,587 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-06-16 22:11:43,598 INFO     35 task_executor_72b43022f5fd_0 reported heartbeat: {"name": "task_executor_72b43022f5fd_0", "now": "2025-06-16T22:11:43.595+08:00", "boot_at": "2025-06-16T22:11:43.506+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-16 22:11:43,601 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-16 22:11:48,686 INFO     35 handle_task begin for task {"id": "d74bf8784abb11f0a36e02420ae90806", "doc_id": "e332090249f811f08c9102420ae90706", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 200, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}
2025-06-16 22:11:48,973 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-16 22:11:49,254 INFO     35 PyTorch version 2.6.0 available.
2025-06-16 22:11:50,781 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-16 22:11:50,973 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-16 22:11:53,864 INFO     35 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.007s]
2025-06-16 22:11:54,211 INFO     35 From minio(0.****************) Bank Holiday Swap Program Guide.pdf/Bank Holiday Swap Program Guide.pdf
2025-06-16 22:11:54,241 INFO     35 set_progress(d74bf8784abb11f0a36e02420ae90806), progress: 0.1, progress_msg: 22:11:54 Page(1~5): Start to parse.
2025-06-16 22:11:54,242 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-16 22:11:54,262 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-16 22:11:54,287 INFO     35 set_progress(d74bf8784abb11f0a36e02420ae90806), progress: -1, progress_msg: 22:11:54 Page(1~5): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named ditod
2025-06-16 22:11:54,287 ERROR    35 Chunking Bank Holiday Swap Program Guide.pdf/Bank Holiday Swap Program Guide.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 274, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 287, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-16 22:11:54,322 INFO     35 set_progress(d74bf8784abb11f0a36e02420ae90806), progress: -1, progress_msg: 22:11:54 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-16 22:11:54,323 ERROR    35 handle_task got exception for task {"id": "d74bf8784abb11f0a36e02420ae90806", "doc_id": "e332090249f811f08c9102420ae90706", "from_page": 0, "to_page": 4, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Bank Holiday Swap Program Guide.pdf", "type": "pdf", "location": "Bank Holiday Swap Program Guide.pdf", "size": 482309, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 200, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": *************, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 274, in __init__
    from ditod.layout_detection import LayoutDetection
ModuleNotFoundError: No module named 'ditod'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 287, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'ditod'
2025-06-16 22:12:13,603 INFO     35 task_executor_72b43022f5fd_0 reported heartbeat: {"name": "task_executor_72b43022f5fd_0", "now": "2025-06-16T22:12:13.601+08:00", "boot_at": "2025-06-16T22:11:43.506+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-16 22:12:13,608 INFO     35 task_executor_7f32c4f98348_0 expired, removed
2025-06-16 22:12:43,616 INFO     35 task_executor_72b43022f5fd_0 reported heartbeat: {"name": "task_executor_72b43022f5fd_0", "now": "2025-06-16T22:12:43.614+08:00", "boot_at": "2025-06-16T22:11:43.506+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
