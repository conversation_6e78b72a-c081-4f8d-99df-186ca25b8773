2025-06-13 21:20:24,264 INFO     36 task_executor_810155486def_0 log path: /ragflow/logs/task_executor_810155486def_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-13 21:20:24,264 INFO     36 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-13 21:20:24,267 INFO     36 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-13 21:20:24,268 INFO     36 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-13 21:20:24,277 INFO     36 GET http://es01:9200/ [status:200 duration:0.005s]
2025-06-13 21:20:24,281 INFO     36 HEAD http://es01:9200/ [status:200 duration:0.003s]
2025-06-13 21:20:24,282 INFO     36 Elasticsearch http://es01:9200 is healthy.
2025-06-13 21:20:24,295 WARNING  36 Load term.freq FAIL!
2025-06-13 21:20:24,303 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-06-13 21:20:24,311 WARNING  36 Load term.freq FAIL!
2025-06-13 21:20:24,316 WARNING  36 Realtime synonym is disabled, since no redis connection.
2025-06-13 21:20:24,316 INFO     36 MAX_CONTENT_LENGTH: 134217728
2025-06-13 21:20:24,318 INFO     36 MAX_FILE_COUNT_PER_USER: 0
2025-06-13 21:20:24,327 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:20:24.325+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 1, "done": 0, "failed": 0, "current": {}}
2025-06-13 21:20:24,328 INFO     36 task_executor_5221c0845fc5_0 expired, removed
2025-06-13 21:20:24,330 WARNING  36 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-13 21:20:24,358 INFO     36 handle_task begin for task {"id": "29b413c6485911f0b1a402420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749820823763, "task_type": ""}
2025-06-13 21:20:24,665 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:20:24,940 INFO     36 PyTorch version 2.6.0 available.
2025-06-13 21:20:26,476 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:20:26,665 WARNING  36 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:20:29,370 INFO     36 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.007s]
2025-06-13 21:20:29,447 INFO     36 From minio(0.07653263956308365) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-13 21:20:29,476 INFO     36 set_progress(29b413c6485911f0b1a402420ae90706), progress: 0.1, progress_msg: 21:20:29 Page(1~3): Start to parse.
2025-06-13 21:20:29,477 INFO     36 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-13 21:20:29,495 INFO     36 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-13 21:20:29,570 INFO     36 set_progress(29b413c6485911f0b1a402420ae90706), progress: -1, progress_msg: 21:20:29 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named detectron2
2025-06-13 21:20:29,570 ERROR    36 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:20:29,600 INFO     36 set_progress(29b413c6485911f0b1a402420ae90706), progress: -1, progress_msg: 21:20:29 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:20:29,600 ERROR    36 handle_task got exception for task {"id": "29b413c6485911f0b1a402420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749820823763, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:20:54,332 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:20:54.330+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:21:24,339 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:21:24.337+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:21:54,348 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:21:54.346+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:22:24,355 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:22:24.353+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:22:54,363 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:22:54.361+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:23:24,370 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:23:24.368+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:23:54,377 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:23:54.375+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:24:24,384 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:24:24.382+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:24:54,390 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:24:54.388+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:25:24,396 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:25:24.394+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:25:54,403 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:25:54.401+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:26:24,410 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:26:24.408+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:26:54,416 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:26:54.414+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:27:24,423 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:27:24.421+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:27:54,429 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:27:54.427+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:28:24,435 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:28:24.434+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:28:54,442 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:28:54.440+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:29:24,451 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:29:24.449+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:29:54,457 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:29:54.455+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:30:24,464 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:30:24.462+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:30:54,470 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:30:54.469+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:31:24,476 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:31:24.474+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:31:54,483 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:31:54.481+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:32:24,490 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:32:24.488+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:32:54,500 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:32:54.498+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:33:24,506 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:33:24.504+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:33:54,513 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:33:54.511+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:34:24,519 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:34:24.518+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:34:54,524 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:34:54.522+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:35:24,531 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:35:24.529+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:35:54,537 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:35:54.535+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:36:24,543 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:36:24.542+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:36:54,551 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:36:54.549+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:37:24,564 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:37:24.562+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:37:54,571 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:37:54.569+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:38:24,578 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:38:24.576+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:38:54,584 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:38:54.583+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:39:24,591 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:39:24.589+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:39:54,597 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:39:54.595+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:40:24,604 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:40:24.602+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:40:54,611 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:40:54.609+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:41:24,617 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:41:24.615+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:41:54,628 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:41:54.626+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:42:24,635 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:42:24.633+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:42:54,641 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:42:54.639+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:43:24,647 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:43:24.645+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:43:54,654 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:43:54.652+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:44:24,660 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:44:24.659+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:44:54,665 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:44:54.663+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:45:24,668 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:45:24.667+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:45:54,673 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:45:54.671+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:46:24,677 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:46:24.676+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:46:54,681 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:46:54.680+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:47:24,687 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:47:24.685+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:47:54,693 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:47:54.692+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:48:24,698 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:48:24.696+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:48:54,705 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:48:54.703+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:49:24,711 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:49:24.709+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:49:54,718 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:49:54.716+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:50:24,722 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:50:24.720+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:50:54,731 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:50:54.729+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:51:24,737 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:51:24.736+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:51:54,743 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:51:54.741+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:52:24,750 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:52:24.748+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:52:54,757 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:52:54.755+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:53:24,764 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:53:24.762+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:53:54,771 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:53:54.769+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:54:24,778 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:54:24.776+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:54:54,791 INFO     36 task_executor_810155486def_0 reported heartbeat: {"name": "task_executor_810155486def_0", "now": "2025-06-13T21:54:54.790+08:00", "boot_at": "2025-06-13T21:20:24.258+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
