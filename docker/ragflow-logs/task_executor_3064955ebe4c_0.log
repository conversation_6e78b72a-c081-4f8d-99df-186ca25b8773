2025-06-12 19:13:37,224 INFO     34 task_executor_3064955ebe4c_0 log path: /ragflow/logs/task_executor_3064955ebe4c_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-12 19:13:37,225 INFO     34 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-12 19:13:37,226 INFO     34 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-12 19:13:37,229 INFO     34 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-12 19:13:37,235 INFO     34 GET http://es01:9200/ [status:200 duration:0.005s]
2025-06-12 19:13:37,239 INFO     34 HEAD http://es01:9200/ [status:200 duration:0.003s]
2025-06-12 19:13:37,240 INFO     34 Elasticsearch http://es01:9200 is healthy.
2025-06-12 19:13:37,254 WARNING  34 Load term.freq FAIL!
2025-06-12 19:13:37,260 WARNING  34 Realtime synonym is disabled, since no redis connection.
2025-06-12 19:13:37,267 WARNING  34 Load term.freq FAIL!
2025-06-12 19:13:37,271 WARNING  34 Realtime synonym is disabled, since no redis connection.
2025-06-12 19:13:37,272 INFO     34 MAX_CONTENT_LENGTH: 134217728
2025-06-12 19:13:37,274 INFO     34 MAX_FILE_COUNT_PER_USER: 0
2025-06-12 19:13:37,286 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:13:37,287 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:13:37.285+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:13:37,291 INFO     34 task_executor_2b754430819b_0 expired, removed
2025-06-12 19:13:37,294 WARNING  34 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-12 19:13:37,295 WARNING  34 RedisDB.get_unacked_iterator queue rag_flow_svr_queue doesn't exist
2025-06-12 19:14:07,295 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:14:07,297 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:14:07.294+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:14:37,304 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:14:37,307 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:14:37.304+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:15:07,315 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:15:07,318 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:15:07.314+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:15:37,327 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:15:37,331 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:15:37.326+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:16:07,340 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:16:07,342 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:16:07.339+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:16:37,350 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:16:37,351 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:16:37.349+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:17:07,361 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:17:07,364 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:17:07.361+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:17:37,371 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:17:37,373 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:17:37.371+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:18:07,380 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:18:07,383 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:18:07.379+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:18:37,391 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:18:37,393 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:18:37.390+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:19:07,400 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:19:07,404 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:19:07.399+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:19:37,415 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:19:37,416 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:19:37.414+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:20:07,426 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:20:07,430 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:20:07.425+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:20:37,438 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:20:37,440 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:20:37.437+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:21:07,447 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:21:07,450 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:21:07.446+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:21:37,457 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:21:37,458 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:21:37.456+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:22:07,465 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:22:07,468 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:22:07.464+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:22:37,475 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:22:37,478 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:22:37.474+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:23:07,484 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:23:07,486 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:23:07.483+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:23:37,498 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:23:37,500 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:23:37.498+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:24:07,508 WARNING  34 RedisDB.queue_info rag_flow_svr_queue got exception: no such key
2025-06-12 19:24:07,511 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:24:07.507+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-12 19:24:32,661 INFO     34 handle_task begin for task {"id": "cfbd64c8477f11f08bcd02420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749727472036, "task_type": ""}
2025-06-12 19:24:33,532 WARNING  34 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-12 19:24:33,875 INFO     34 PyTorch version 2.6.0 available.
2025-06-12 19:24:35,900 WARNING  34 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-12 19:24:36,090 WARNING  34 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-12 19:24:38,862 INFO     34 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:404 duration:0.010s]
2025-06-12 19:24:39,241 INFO     34 PUT http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.375s]
2025-06-12 19:24:39,266 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:24:39.261+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 1, "lag": 0, "done": 0, "failed": 0, "current": {"cfbd64c8477f11f08bcd02420ae90706": {"id": "cfbd64c8477f11f08bcd02420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749727472036, "task_type": ""}}}
2025-06-12 19:24:39,661 INFO     34 From minio(0.41969238966703415) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-12 19:24:39,689 INFO     34 set_progress(cfbd64c8477f11f08bcd02420ae90706), progress: 0.1, progress_msg: 19:24:39 Page(1~3): Start to parse.
2025-06-12 19:24:39,690 INFO     34 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-12 19:24:39,709 INFO     34 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-12 19:24:39,737 INFO     34 set_progress(cfbd64c8477f11f08bcd02420ae90706), progress: -1, progress_msg: 19:24:39 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named detectron2
2025-06-12 19:24:39,737 ERROR    34 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-12 19:24:39,777 INFO     34 set_progress(cfbd64c8477f11f08bcd02420ae90706), progress: -1, progress_msg: 19:24:39 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-12 19:24:39,778 ERROR    34 handle_task got exception for task {"id": "cfbd64c8477f11f08bcd02420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749727472036, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-12 19:25:09,276 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:25:09.274+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:25:39,285 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:25:39.283+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:26:09,292 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:26:09.290+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:26:39,300 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:26:39.298+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:27:09,307 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:27:09.305+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:27:39,314 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:27:39.312+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:28:09,321 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:28:09.319+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:28:39,328 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:28:39.326+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:29:09,336 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:29:09.334+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:29:39,348 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:29:39.342+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:30:09,357 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:30:09.355+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:30:39,363 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:30:39.361+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:31:09,370 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:31:09.368+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-12 19:31:39,378 INFO     34 task_executor_3064955ebe4c_0 reported heartbeat: {"name": "task_executor_3064955ebe4c_0", "now": "2025-06-12T19:31:39.376+08:00", "boot_at": "2025-06-12T19:13:37.221+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
