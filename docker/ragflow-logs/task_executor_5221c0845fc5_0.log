2025-06-13 21:12:40,819 INFO     35 task_executor_5221c0845fc5_0 log path: /ragflow/logs/task_executor_5221c0845fc5_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-06-13 21:12:40,819 INFO     35 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-06-13 21:12:40,822 INFO     35 TaskExecutor: RAGFlow version: v0.19.0 full
2025-06-13 21:12:40,826 INFO     35 Use Elasticsearch http://es01:9200 as the doc engine.
2025-06-13 21:12:40,834 INFO     35 GET http://es01:9200/ [status:200 duration:0.004s]
2025-06-13 21:12:40,836 INFO     35 HEAD http://es01:9200/ [status:200 duration:0.002s]
2025-06-13 21:12:40,840 INFO     35 Elasticsearch http://es01:9200 is healthy.
2025-06-13 21:12:40,850 WARNING  35 Load term.freq FAIL!
2025-06-13 21:12:40,856 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-13 21:12:40,862 WARNING  35 Load term.freq FAIL!
2025-06-13 21:12:40,866 WARNING  35 Realtime synonym is disabled, since no redis connection.
2025-06-13 21:12:40,866 INFO     35 MAX_CONTENT_LENGTH: 134217728
2025-06-13 21:12:40,869 INFO     35 MAX_FILE_COUNT_PER_USER: 0
2025-06-13 21:12:40,878 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:12:40.876+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-06-13 21:12:40,881 INFO     35 task_executor_e800d7e53083_0 expired, removed
2025-06-13 21:12:40,884 WARNING  35 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-06-13 21:12:41,321 INFO     35 handle_task begin for task {"id": "160a4198485811f0a28d02420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749820361277, "task_type": ""}
2025-06-13 21:12:41,538 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:441: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:12:41,814 INFO     35 PyTorch version 2.6.0 available.
2025-06-13 21:12:43,349 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:12:43,543 WARNING  35 /ragflow/.venv/lib/python3.10/site-packages/transformers/utils/generic.py:309: FutureWarning: `torch.utils._pytree._register_pytree_node` is deprecated. Please use `torch.utils._pytree.register_pytree_node` instead.
  _torch_pytree._register_pytree_node(

2025-06-13 21:12:46,340 INFO     35 HEAD http://es01:9200/ragflow_ebd47f2646b611f0821c02420ae90706 [status:200 duration:0.005s]
2025-06-13 21:12:46,372 INFO     35 From minio(0.031792037189006805) Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf
2025-06-13 21:12:46,401 INFO     35 set_progress(160a4198485811f0a28d02420ae90706), progress: 0.1, progress_msg: 21:12:46 Page(1~3): Start to parse.
2025-06-13 21:12:46,402 INFO     35 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-06-13 21:12:46,420 INFO     35 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-06-13 21:12:46,446 INFO     35 set_progress(160a4198485811f0a28d02420ae90706), progress: -1, progress_msg: 21:12:46 Page(1~3): [ERROR]Internal server error while chunking: Failed to import custom DiT model. Make sure dependencies are installed: No module named detectron2
2025-06-13 21:12:46,447 ERROR    35 Chunking Liheyu Zhang - Employment Reference Letter 02042025.pdf/Liheyu Zhang - Employment Reference Letter 02042025.pdf got exception
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:12:46,478 INFO     35 set_progress(160a4198485811f0a28d02420ae90706), progress: -1, progress_msg: 21:12:46 [ERROR][Exception]: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:12:46,479 ERROR    35 handle_task got exception for task {"id": "160a4198485811f0a28d02420ae90706", "doc_id": "cf8d501c477f11f0954302420ae90706", "from_page": 0, "to_page": 2, "retry_count": 0, "kb_id": "a335f3ec46d611f0a3dc02420ae90706", "parser_id": "naive", "parser_config": {"pages": [[1, 1000000]]}, "name": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "type": "pdf", "location": "Liheyu Zhang - Employment Reference Letter 02042025.pdf", "size": 160834, "tenant_id": "ebd47f2646b611f0821c02420ae90706", "language": "English", "embd_id": "BAAI/bge-large-zh-v1.5@BAAI", "pagerank": 0, "kb_parser_config": {"pages": [[1, 1000000]]}, "img2txt_id": "", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1749820361277, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 282, in __init__
    from ditod.layout_detection import LayoutDetection
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/__init__.py", line 10, in <module>
    from .config import add_vit_config
  File "/ragflow/page_layout_detection-v2.0.0/dit_aug/ditod/config.py", line 1, in <module>
    from detectron2.config import CfgNode as CN
ModuleNotFoundError: No module named 'detectron2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 419, in chunk
    pdf_parser = Pdf()
  File "/ragflow/rag/app/naive.py", line 249, in __init__
    super().__init__()
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 70, in __init__
    self.layouter = LayoutRecognizer("layout")
  File "/ragflow/deepdoc/vision/layout_recognizer.py", line 291, in __init__
    raise ImportError(f"Failed to import custom DiT model. Make sure dependencies are installed: {e}")
ImportError: Failed to import custom DiT model. Make sure dependencies are installed: No module named 'detectron2'
2025-06-13 21:13:10,887 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:13:10.885+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:13:40,894 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:13:40.892+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:14:10,902 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:14:10.900+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:14:40,911 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:14:40.908+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:15:10,926 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:15:10.925+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:15:40,933 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:15:40.931+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
2025-06-13 21:16:10,940 INFO     35 task_executor_5221c0845fc5_0 reported heartbeat: {"name": "task_executor_5221c0845fc5_0", "now": "2025-06-13T21:16:10.938+08:00", "boot_at": "2025-06-13T21:12:40.812+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 1, "current": {}}
