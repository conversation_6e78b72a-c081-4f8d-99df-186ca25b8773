# RAGFlow Document Processing Pipeline - Step-by-Step Guide

## Overview
This enhanced pipeline processes PDF documents through RAGFlow's internal functions and saves outputs from each major step for analysis and debugging.

## Your File Paths
- **Input PDF**: `/ml/shared/AI_Lab/g2module_docs/g2mod.pdf`
- **Output Directory**: `//eoaasvm004/ml/shared/lazhang/data`

## Processing Steps and Outputs

### Step 1: PDF Parsing 📄
**Function**: `parse_pdf_document()`
**What it does**: Extracts text and tables from the PDF using RAGFlow's internal PDF parser
**Output file**: `01_pdf_parsing.json`
**Contains**:
- PDF file path
- Number of sections and tables extracted
- Raw text content from all sections
- Table data

### Step 2: Content Chunking ✂️
**Function**: `chunk_content()`
**What it does**: Splits the extracted content into manageable chunks using RAGFlow's naive merge algorithm
**Output file**: `02_content_chunking.json`
**Contains**:
- Document metadata (doc_id, kb_id, etc.)
- Number of chunks created
- Content of each chunk with length information
- Chunking parameters used

### Step 3: Tokenization 🔤
**Function**: `tokenize_and_prepare_chunks()`
**What it does**: Tokenizes chunks and prepares them for Elasticsearch storage
**Output file**: `03_tokenization.json`
**Contains**:
- Tokenized chunks with all metadata
- Document base information
- Chunk IDs and positions
- Important keywords extracted

### Step 4: Embedding Generation 🧮
**Function**: `generate_embeddings()`
**What it does**: Generates vector embeddings for each chunk using RAGFlow's embedding model
**Output file**: `04_embedding_generation.json`
**Contains**:
- Number of embeddings generated
- Vector dimensions
- Token count used
- Sample embedding vector
- Vector field names

### Step 5: Elasticsearch Storage 💾
**Function**: `store_in_elasticsearch()`
**What it does**: Stores the embedded chunks in Elasticsearch database
**Output file**: `05_elasticsearch_storage.json`
**Contains**:
- Elasticsearch index name
- Knowledge base ID
- Vector size configuration
- Number of chunks stored
- Storage timestamp

### Step 6: Chunk Extraction 🔍
**Function**: `extract_chunks_from_es()`
**What it does**: Extracts the final processed chunks from Elasticsearch
**Output files**: 
- `06_chunk_extraction.json` - Raw extracted data
- `06_readable_chunks.json` - Human-readable format
**Contains**:
- All processed chunks with embeddings
- Readable format with just content and keywords
- Extraction metadata

## How to Run

### Option 1: Use the default paths (your specific file)
```bash
python main_direct.py
```

### Option 2: Use the dedicated script
```bash
python run_g2mod_processing.py
```

### Option 3: Specify custom paths
```bash
python main_direct.py "/ml/shared/AI_Lab/g2module_docs/g2mod.pdf" "//eoaasvm004/ml/shared/lazhang/data"
```

## Output Directory Structure
```
//eoaasvm004/ml/shared/lazhang/data/
└── g2mod_YYYYMMDD_HHMMSS/
    ├── 01_pdf_parsing.json
    ├── 02_content_chunking.json
    ├── 03_tokenization.json
    ├── 04_embedding_generation.json
    ├── 05_elasticsearch_storage.json
    ├── 06_chunk_extraction.json
    └── 06_readable_chunks.json
```

## Key Features
- ✅ Saves output from every major processing step
- ✅ Timestamped output directories to avoid conflicts
- ✅ Human-readable chunk format for easy analysis
- ✅ Error handling with fallback extraction methods
- ✅ Detailed logging of each step's progress
- ✅ JSON format for easy programmatic access

## Troubleshooting
- If `extract_es_chunks` module is not found, the script will use basic Elasticsearch extraction
- All outputs are saved in JSON format with UTF-8 encoding
- Check the console output for detailed progress information
- Each step's success/failure is clearly indicated with emojis

## Next Steps
After processing, you can:
1. Analyze the step outputs to understand the processing pipeline
2. Use the readable chunks for content analysis
3. Query the Elasticsearch index for similarity search
4. Modify chunking parameters and reprocess if needed
