#!/usr/bin/env python3
"""
Direct test of layout models by temporarily switching the active recognizer
and capturing detailed logging output to compare YOLO vs DiT table detection
"""

import os
import sys
import time
import shutil
import subprocess
from datetime import datetime

# Add RAGFlow paths
sys.path.insert(0, '/ml/shared/lazhang/code/viridoc')

def backup_and_modify_init_file(use_yolo=False):
    """Backup and modify the __init__.py file to switch layout recognizers"""
    init_file = "/ml/shared/lazhang/code/viridoc/deepdoc/vision/__init__.py"
    backup_file = f"{init_file}.backup_{int(time.time())}"
    
    # Create backup
    shutil.copy2(init_file, backup_file)
    print(f"📋 Backup created: {backup_file}")
    
    # Read current content
    with open(init_file, 'r') as f:
        content = f.read()
    
    if use_yolo:
        # Switch to YOLO
        new_content = content.replace(
            "from .layout_recognizer import CustomDiTLayoutRecognizer as LayoutRecognizer",
            "#from .layout_recognizer import CustomDiTLayoutRecognizer as LayoutRecognizer\nfrom .layout_recognizer import LayoutRecognizer4YOLOv10 as LayoutRecognizer"
        )
        print("🔄 Switched to YOLO layout recognizer")
    else:
        # Switch to DiT (restore)
        new_content = content.replace(
            "#from .layout_recognizer import CustomDiTLayoutRecognizer as LayoutRecognizer\nfrom .layout_recognizer import LayoutRecognizer4YOLOv10 as LayoutRecognizer",
            "from .layout_recognizer import CustomDiTLayoutRecognizer as LayoutRecognizer"
        )
        print("🔄 Switched to DiT layout recognizer")
    
    # Write modified content
    with open(init_file, 'w') as f:
        f.write(new_content)
    
    return backup_file

def restore_init_file(backup_file):
    """Restore the original __init__.py file"""
    init_file = "/ml/shared/lazhang/code/viridoc/deepdoc/vision/__init__.py"
    shutil.copy2(backup_file, init_file)
    os.remove(backup_file)
    print(f"✅ Restored original configuration")

def test_layout_model_direct(model_name, test_pdf_path):
    """Test layout model directly using RAGFlow's internal functions"""
    print(f"\n{'='*20} Testing {model_name} Model {'='*20}")
    
    try:
        # Import after switching models
        from deepdoc.vision import LayoutRecognizer
        from deepdoc.vision.ocr import OCR
        from deepdoc.parser.pdf_parser import Pdf
        import fitz  # PyMuPDF
        
        print(f"📄 Processing: {os.path.basename(test_pdf_path)}")
        print(f"🔧 Active recognizer: {LayoutRecognizer.__name__}")
        
        # Initialize components
        layout_recognizer = LayoutRecognizer("layout")
        ocr = OCR()
        
        # Open PDF and extract images
        doc = fitz.open(test_pdf_path)
        images = []
        
        # Process first few pages (limit for testing)
        max_pages = min(3, len(doc))
        print(f"📖 Processing {max_pages} pages...")
        
        for page_num in range(max_pages):
            page = doc[page_num]
            # Convert to image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # Save temporary image for processing
            temp_img_path = f"/tmp/test_page_{page_num}_{model_name.lower()}.png"
            with open(temp_img_path, "wb") as f:
                f.write(img_data)
            images.append(temp_img_path)
        
        doc.close()
        
        # Process with layout recognizer
        print(f"🔍 Running {model_name} layout recognition...")
        start_time = time.time()
        
        # Capture detailed output
        log_file = f"layout_test_{model_name.lower()}_{int(time.time())}.log"
        
        # Run layout recognition with logging
        layouts = layout_recognizer.forward(images, thr=0.2)
        
        processing_time = time.time() - start_time
        print(f"⏱️ Processing time: {processing_time:.2f}s")
        
        # Analyze results
        total_detections = 0
        table_detections = 0
        detection_summary = {}
        
        for page_idx, page_layouts in enumerate(layouts):
            print(f"\n📄 Page {page_idx + 1} results:")
            print(f"   Total detections: {len(page_layouts)}")
            
            page_tables = 0
            page_summary = {}
            
            for detection in page_layouts:
                det_type = detection.get("type", "unknown").lower()
                page_summary[det_type] = page_summary.get(det_type, 0) + 1
                
                if "table" in det_type:
                    page_tables += 1
                    table_detections += 1
                    print(f"   🔍 TABLE detected: {detection}")
            
            total_detections += len(page_layouts)
            print(f"   Tables found: {page_tables}")
            print(f"   Detection types: {page_summary}")
            
            # Update global summary
            for det_type, count in page_summary.items():
                detection_summary[det_type] = detection_summary.get(det_type, 0) + count
        
        # Clean up temp images
        for img_path in images:
            if os.path.exists(img_path):
                os.remove(img_path)
        
        results = {
            "model": model_name,
            "recognizer_class": LayoutRecognizer.__name__,
            "processing_time": processing_time,
            "total_detections": total_detections,
            "table_detections": table_detections,
            "detection_summary": detection_summary,
            "pages_processed": max_pages
        }
        
        print(f"\n📊 {model_name} Summary:")
        print(f"   Recognizer class: {LayoutRecognizer.__name__}")
        print(f"   Total detections: {total_detections}")
        print(f"   Table detections: {table_detections}")
        print(f"   Processing time: {processing_time:.2f}s")
        print(f"   Detection types: {detection_summary}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing {model_name}: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "model": model_name,
            "error": str(e),
            "success": False
        }

def main():
    print("🧪 Direct Layout Model Comparison: YOLO vs DiT")
    print("=" * 60)
    
    # Test file
    test_pdf = "/ml/shared/lazhang/data/pdfdata/g2mod.pdf"
    
    if not os.path.exists(test_pdf):
        print(f"❌ Test file not found: {test_pdf}")
        return
    
    results = {}
    
    try:
        # Test 1: DiT Model (current default)
        print("\n🔬 Testing DiT Model (Current Default)")
        results["DiT"] = test_layout_model_direct("DiT", test_pdf)
        
        # Test 2: Switch to YOLO and test
        print("\n🔄 Switching to YOLO Model...")
        backup_file = backup_and_modify_init_file(use_yolo=True)
        
        # Clear Python module cache to force reload
        modules_to_clear = [
            'deepdoc.vision',
            'deepdoc.vision.layout_recognizer',
            'deepdoc.parser.pdf_parser'
        ]
        
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
        print("🔬 Testing YOLO Model")
        results["YOLO"] = test_layout_model_direct("YOLO", test_pdf)
        
        # Restore original configuration
        print("\n🔄 Restoring original configuration...")
        restore_init_file(backup_file)
        
        # Clear cache again
        for module in modules_to_clear:
            if module in sys.modules:
                del sys.modules[module]
        
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Compare results
    print(f"\n{'='*20} COMPARISON RESULTS {'='*20}")
    
    for model, result in results.items():
        print(f"\n{model} Model:")
        if result.get("success", True):
            print(f"  Recognizer: {result.get('recognizer_class', 'Unknown')}")
            print(f"  Processing time: {result.get('processing_time', 0):.2f}s")
            print(f"  Total detections: {result.get('total_detections', 0)}")
            print(f"  Table detections: {result.get('table_detections', 0)}")
            print(f"  Detection types: {result.get('detection_summary', {})}")
        else:
            print(f"  ❌ Failed: {result.get('error', 'Unknown error')}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"direct_layout_comparison_{timestamp}.json"
    
    import json
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    # Analysis
    if "DiT" in results and "YOLO" in results:
        dit_tables = results["DiT"].get("table_detections", 0)
        yolo_tables = results["YOLO"].get("table_detections", 0)
        
        print(f"\n🔍 Table Detection Analysis:")
        print(f"   DiT found {dit_tables} tables")
        print(f"   YOLO found {yolo_tables} tables")
        
        if dit_tables != yolo_tables:
            print(f"   ⚠️ Different table detection counts!")
            print(f"   Difference: {abs(dit_tables - yolo_tables)} tables")
        else:
            print(f"   ✅ Both models found the same number of tables")

if __name__ == "__main__":
    main()
