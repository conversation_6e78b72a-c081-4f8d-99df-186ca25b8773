#!/usr/bin/env python3
"""
Helper script to get RAGFlow API key
"""

import requests
import json
import getpass

def get_ragflow_api_key(server_url, username=None, password=None):
    """
    Get RAGFlow API key by logging in
    Note: This is a helper function - you may need to adjust based on your RAGFlow setup
    """
    
    if not username:
        username = input("Enter your RAGFlow username: ")
    
    if not password:
        password = getpass.getpass("Enter your RAGFlow password: ")
    
    # Login endpoint (this may vary based on RAGFlow version)
    login_url = f"{server_url.rstrip('/')}/api/v1/login"
    
    login_data = {
        "email": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get("code") == 0:
            # Extract token/API key from response
            token = result.get("data", {}).get("access_token")
            if token:
                print(f"✅ Successfully obtained API key!")
                print(f"🔑 Your API Key: {token}")
                print(f"\n📝 To use this key:")
                print(f"   export RAGFLOW_API_KEY='{token}'")
                print(f"   python main_api.py")
                return token
            else:
                print("❌ No API key found in response")
                return None
        else:
            print(f"❌ Login failed: {result.get('message', 'Unknown error')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None

def main():
    print("🔑 RAGFlow API Key Helper")
    print("=" * 40)
    
    server_url = "http://eoaagmld007.int.cgg.com"
    print(f"🌐 Server: {server_url}")
    print()
    
    print("📋 Instructions:")
    print("1. Make sure you have a RAGFlow account")
    print("2. Enter your login credentials below")
    print("3. The script will attempt to get your API key")
    print()
    print("⚠️  Alternative method:")
    print("   - Log into RAGFlow web interface")
    print("   - Go to Settings -> API Keys")
    print("   - Create or copy your API key")
    print()
    
    choice = input("Do you want to try automatic login? (y/n): ").lower().strip()
    
    if choice == 'y':
        api_key = get_ragflow_api_key(server_url)
        if api_key:
            # Save to file for convenience
            with open('.ragflow_api_key', 'w') as f:
                f.write(api_key)
            print(f"💾 API key also saved to .ragflow_api_key file")
    else:
        print("\n📝 Manual steps:")
        print(f"1. Open {server_url} in your browser")
        print("2. Log in to your account")
        print("3. Navigate to Settings -> API Keys")
        print("4. Create a new API key or copy existing one")
        print("5. Set environment variable: export RAGFLOW_API_KEY='your-key'")
        print("6. Run: python main_api.py")

if __name__ == "__main__":
    main()
