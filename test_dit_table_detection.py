#!/usr/bin/env python3
"""
Direct test to see if DiT is detecting tables in your PDFs
This bypasses the API and tests the PDF processing directly
"""

import sys
import os
import json
from pathlib import Path

# Add RAGFlow path
sys.path.append('/ml/shared/lazhang/code/viridoc')

def test_dit_detection_directly():
    """Test DiT detection directly on your PDFs"""
    
    print("🔍 DIRECT DiT TABLE DETECTION TEST")
    print("="*60)
    
    # Test PDFs
    pdf_dir = "/ml/shared/lazhang/data/pdfdata"
    pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
    
    print(f"📄 Found {len(pdf_files)} PDF files: {pdf_files}")
    
    for pdf_file in pdf_files:
        pdf_path = os.path.join(pdf_dir, pdf_file)
        print(f"\n🔍 Testing: {pdf_file}")
        print("-" * 40)
        
        try:
            # Import and test PDF processing
            from deepdoc.parser.pdf_parser import Pdf
            
            # Create PDF parser
            pdf_parser = Pdf()
            
            # Process the PDF
            print(f"📄 Processing {pdf_file}...")
            sections, tables = pdf_parser(pdf_path)
            
            print(f"✅ Processing completed!")
            print(f"   📄 Sections: {len(sections)}")
            print(f"   📊 Tables: {len(tables)}")
            
            # Check if we have page layout data
            if hasattr(pdf_parser, 'page_layout'):
                print(f"   📋 Pages processed: {len(pdf_parser.page_layout)}")
                
                total_table_regions = 0
                for page_idx, page_layout in enumerate(pdf_parser.page_layout):
                    table_regions = [item for item in page_layout if item.get('type') == 'table']
                    total_table_regions += len(table_regions)
                    print(f"   📄 Page {page_idx}: {len(table_regions)} table regions detected")
                    
                    # Show details of detected tables
                    for i, table in enumerate(table_regions):
                        print(f"      🔲 Table {i}: bbox=({table.get('x0', 0):.1f}, {table.get('top', 0):.1f}, {table.get('x1', 0):.1f}, {table.get('bottom', 0):.1f}), score={table.get('score', 0):.3f}")
                
                print(f"   🎯 Total DiT table detections: {total_table_regions}")
                
                # Check table components
                if hasattr(pdf_parser, 'tb_cpns'):
                    print(f"   🔧 Table components generated: {len(pdf_parser.tb_cpns)}")
                    
                    # Show sample table components
                    for i, comp in enumerate(pdf_parser.tb_cpns[:5]):
                        print(f"      Component {i}: {comp.get('label', 'unknown')} at page {comp.get('pn', 'unknown')}")
                else:
                    print(f"   ⚠️  No table components found")
                    
            else:
                print(f"   ⚠️  No page layout data available")
                
            # Analysis
            print(f"\n📊 ANALYSIS FOR {pdf_file}:")
            if hasattr(pdf_parser, 'page_layout'):
                dit_tables = sum(len([item for item in page if item.get('type') == 'table']) 
                               for page in pdf_parser.page_layout)
                table_components = len(pdf_parser.tb_cpns) if hasattr(pdf_parser, 'tb_cpns') else 0
                final_tables = len(tables)
                
                print(f"   🤖 DiT detected: {dit_tables} table regions")
                print(f"   🔧 Table components: {table_components}")
                print(f"   📊 Final tables: {final_tables}")
                
                if dit_tables > 0 and table_components > 0 and final_tables > 0:
                    print(f"   ✅ Table processing chain is working!")
                elif dit_tables > 0 and table_components == 0:
                    print(f"   ⚠️  DiT detected tables but TableStructureRecognizer didn't process them")
                elif dit_tables == 0:
                    print(f"   ⚠️  DiT didn't detect any tables")
                else:
                    print(f"   ⚠️  Table processing chain has issues")
            
        except Exception as e:
            print(f"❌ Error processing {pdf_file}: {e}")
            import traceback
            traceback.print_exc()

def test_dit_api_directly():
    """Test DiT API directly with a sample image"""
    print(f"\n🔍 TESTING DiT API DIRECTLY")
    print("="*40)
    
    try:
        from deepdoc.vision.layout_recognizer import CustomDiTLayoutRecognizer
        from PIL import Image
        import requests
        
        # Test DiT API connectivity
        dit_api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
        
        print(f"🌐 Testing DiT API: {dit_api_url}")
        response = requests.get(dit_api_url, timeout=10)
        print(f"✅ DiT API Status: {response.status_code}")
        
        # Create a test recognizer
        recognizer = CustomDiTLayoutRecognizer()
        print(f"✅ DiT recognizer created successfully")
        
        # Test with first PDF page
        pdf_dir = "/ml/shared/lazhang/data/pdfdata"
        pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
        
        if pdf_files:
            pdf_path = os.path.join(pdf_dir, pdf_files[0])
            print(f"📄 Testing with: {pdf_files[0]}")
            
            # Convert first page to image
            import fitz  # PyMuPDF
            doc = fitz.open(pdf_path)
            page = doc[0]
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom
            img_data = pix.tobytes("png")
            
            from io import BytesIO
            image = Image.open(BytesIO(img_data))
            print(f"🖼️  Image size: {image.size}")
            
            # Test DiT API call
            api_result = recognizer.call_api(image)
            print(f"🤖 DiT API Result: {type(api_result)}")
            
            if api_result and 'outputs' in api_result:
                classes = api_result.get('outputs', {}).get('classes', [])
                scores = api_result.get('outputs', {}).get('scores', [])
                boxes = api_result.get('outputs', {}).get('boxes', [])
                
                print(f"   📊 Total detections: {len(classes)}")
                print(f"   📊 Classes: {set(classes)}")
                
                table_count = classes.count('TABLE')
                print(f"   🔲 TABLE detections: {table_count}")
                
                # Show table detections
                for i, (cls, score, box) in enumerate(zip(classes, scores, boxes)):
                    if cls == 'TABLE':
                        print(f"      Table {i}: score={score:.3f}, bbox={box}")
                        
            else:
                print(f"   ⚠️  No valid API result")
                
            doc.close()
            
    except Exception as e:
        print(f"❌ Error testing DiT API: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🧪 DiT TABLE DETECTION DIAGNOSTIC")
    print("="*80)
    print("This will test if DiT is actually detecting tables in your PDFs")
    print()
    
    # Test 1: Direct PDF processing
    test_dit_detection_directly()
    
    # Test 2: Direct DiT API test
    test_dit_api_directly()
    
    print(f"\n📋 DIAGNOSTIC SUMMARY")
    print("="*40)
    print("Look at the output above to see:")
    print("1. Whether DiT detected any table regions")
    print("2. Whether TableStructureRecognizer processed them")
    print("3. Whether final tables were extracted")
    print()
    print("If DiT detected 0 tables, the issue is with table detection")
    print("If DiT detected tables but final tables = 0, the issue is with table processing")

if __name__ == "__main__":
    main()
