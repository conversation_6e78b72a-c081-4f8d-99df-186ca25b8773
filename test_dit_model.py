#!/usr/bin/env python3
"""
Test script to verify DiT model is working correctly with RAGFlow
Tests PDF processing with DiT layout recognition model
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

# Configuration
API_BASE_URL = "http://127.0.0.1:9380"
API_KEY = "ragflow-IwODhkYzYwYzI2N11jNzgxMTVi"
DATASET_NAME = "dit_test_dataset"
PDF_FOLDER = "pdfs"

class DiTModelTester:
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
    def test_dit_api_connectivity(self):
        """Test if DiT API endpoint is reachable"""
        print("🔍 Testing DiT API connectivity...")
        
        dit_api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
        
        try:
            # Test with a simple request (might fail but we check connectivity)
            response = requests.get(dit_api_url, timeout=10)
            print(f"✅ DiT API endpoint is reachable (status: {response.status_code})")
            return True
        except requests.exceptions.RequestException as e:
            print(f"❌ DiT API endpoint unreachable: {e}")
            print("⚠️  This might cause layout recognition to fail")
            return False
    
    def create_dit_dataset(self):
        """Create a dataset specifically configured for DiT model testing"""
        print(f"📦 Creating DiT test dataset: {DATASET_NAME}")
        
        url = f"{self.api_base_url}/api/v1/datasets"
        
        # Use completely default configuration to let DiT model work
        payload = {
            "name": DATASET_NAME,
            "description": "DiT model layout recognition test dataset",
            "chunk_method": "naive"
            # No parser_config - let it use defaults with DiT
        }
        
        try:
            response = requests.post(url, json=payload, headers=self.headers)
            response.raise_for_status()
            
            dataset_info = response.json()
            dataset_id = dataset_info["data"]["id"]
            print(f"✅ Dataset created successfully with ID: {dataset_id}")
            return dataset_id
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to create dataset: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            return None
    
    def upload_and_parse_pdf(self, dataset_id, pdf_path):
        """Upload PDF and parse with DiT model"""
        print(f"📄 Testing DiT model with PDF: {pdf_path}")
        
        # Step 1: Upload PDF
        upload_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                headers_upload = {"Authorization": f"Bearer {self.api_key}"}
                
                response = requests.post(upload_url, files=files, headers=headers_upload)
                response.raise_for_status()
                
                upload_result = response.json()
                document_id = upload_result["data"]["id"]
                print(f"✅ PDF uploaded successfully with ID: {document_id}")
                
        except Exception as e:
            print(f"❌ Failed to upload PDF: {e}")
            return None
        
        # Step 2: Parse with DiT model
        parse_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        parse_payload = {"document_ids": [document_id]}
        
        try:
            print("🔄 Starting DiT model parsing...")
            response = requests.post(parse_url, json=parse_payload, headers=self.headers)
            response.raise_for_status()
            
            print("✅ Parse request submitted successfully")
            
            # Monitor parsing status
            return self.monitor_dit_parsing(dataset_id, document_id)
            
        except Exception as e:
            print(f"❌ Failed to start parsing: {e}")
            return None
    
    def monitor_dit_parsing(self, dataset_id, document_id, max_wait=300):
        """Monitor DiT model parsing progress"""
        print("⏳ Monitoring DiT model parsing progress...")
        
        status_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                response = requests.get(status_url, headers=self.headers)
                response.raise_for_status()
                
                doc_info = response.json()["data"]
                status = doc_info.get("status", "UNKNOWN")
                chunk_num = doc_info.get("chunk_num", 0)
                
                print(f"📊 Status: {status}, Chunks: {chunk_num}")
                
                if status == "DONE":
                    print(f"✅ DiT model parsing completed! Generated {chunk_num} chunks")
                    return self.test_dit_results(dataset_id, document_id, chunk_num)
                elif status == "FAIL":
                    print("❌ DiT model parsing failed!")
                    return False
                
                time.sleep(10)  # Wait 10 seconds before next check
                
            except Exception as e:
                print(f"⚠️  Error checking status: {e}")
                time.sleep(5)
        
        print("⏰ Parsing timeout - DiT model may be taking too long")
        return False
    
    def test_dit_results(self, dataset_id, document_id, chunk_num):
        """Test and analyze DiT model results"""
        print("🧪 Analyzing DiT model results...")
        
        if chunk_num == 0:
            print("❌ DiT model produced 0 chunks - layout recognition may have failed")
            return False
        
        # Get chunk details
        chunks_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        
        try:
            response = requests.get(chunks_url, headers=self.headers)
            response.raise_for_status()
            
            chunks_data = response.json()["data"]
            
            print(f"📋 DiT Model Results Summary:")
            print(f"   Total Chunks: {len(chunks_data)}")
            
            # Analyze layout types detected by DiT
            layout_types = {}
            content_lengths = []
            
            for chunk in chunks_data[:5]:  # Analyze first 5 chunks
                content = chunk.get("content_with_weight", "")
                content_lengths.append(len(content))
                
                # Try to identify layout patterns
                if "title" in content.lower() or len(content) < 50:
                    layout_types["Title/Header"] = layout_types.get("Title/Header", 0) + 1
                elif len(content) > 200:
                    layout_types["Text Block"] = layout_types.get("Text Block", 0) + 1
                else:
                    layout_types["Other"] = layout_types.get("Other", 0) + 1
            
            print(f"   Layout Types Detected: {layout_types}")
            print(f"   Average Content Length: {sum(content_lengths)/len(content_lengths):.1f} chars")
            
            # Show sample content
            if chunks_data:
                sample_content = chunks_data[0].get("content_with_weight", "")[:200]
                print(f"   Sample Content: {sample_content}...")
            
            print("✅ DiT model is working correctly!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to analyze DiT results: {e}")
            return False
    
    def cleanup_test_dataset(self, dataset_id):
        """Clean up test dataset"""
        print(f"🧹 Cleaning up test dataset...")
        
        try:
            delete_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}"
            response = requests.delete(delete_url, headers=self.headers)
            response.raise_for_status()
            print("✅ Test dataset cleaned up successfully")
        except Exception as e:
            print(f"⚠️  Failed to cleanup dataset: {e}")

def main():
    print("🎯 DiT Model Layout Recognition Test")
    print("=" * 50)
    
    # Initialize tester
    tester = DiTModelTester(API_BASE_URL, API_KEY)
    
    # Step 1: Test DiT API connectivity
    dit_reachable = tester.test_dit_api_connectivity()
    
    # Step 2: Find a test PDF
    test_pdf = Path("sdk/python/test/test_sdk_api/test_data/test.pdf")
    if not test_pdf.exists():
        print(f"❌ Test PDF not found: {test_pdf}")
        return
    print(f"📄 Using test PDF: {test_pdf}")
    
    # Step 3: Create test dataset
    dataset_id = tester.create_dit_dataset()
    if not dataset_id:
        return
    
    try:
        # Step 4: Test DiT model with PDF
        success = tester.upload_and_parse_pdf(dataset_id, test_pdf)
        
        if success:
            print("\n🎉 DiT Model Test Results:")
            print("✅ DiT model is working correctly")
            print("✅ Layout recognition is functional")
            print("✅ PDF processing pipeline is complete")
        else:
            print("\n❌ DiT Model Test Failed:")
            if not dit_reachable:
                print("   - DiT API endpoint is unreachable")
                print("   - Check network connectivity and proxy settings")
            print("   - Layout recognition may not be working")
            
    finally:
        # Cleanup
        tester.cleanup_test_dataset(dataset_id)

if __name__ == "__main__":
    main()
