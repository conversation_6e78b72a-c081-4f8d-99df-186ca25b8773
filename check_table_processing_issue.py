#!/usr/bin/env python3
"""
Check why table processing is failing
This investigates the specific issue you mentioned
"""

import requests
import json
import sys
import os

def check_ragflow_chunks():
    """Check the actual chunks generated by RAGFlow to see if tables were processed"""
    
    print("🔍 INVESTIGATING TABLE PROCESSING ISSUE")
    print("="*60)
    
    # RAGFlow API settings
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    
    # Dataset ID from the previous run
    dataset_id = "7173e3a651d911f090a802420ae90806"
    
    print(f"📦 Checking dataset: {dataset_id}")
    
    try:
        # Get chunks from the dataset
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Method 1: Get chunks directly
        chunks_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        params = {
            "offset": 0,
            "limit": 50,
            "keywords": ""
        }
        
        print(f"🔍 Fetching chunks from: {chunks_url}")
        response = requests.get(chunks_url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            chunks = data.get('data', {}).get('chunks', [])
            
            print(f"✅ Retrieved {len(chunks)} chunks")
            
            # Analyze chunks for table content
            table_chunks = 0
            table_keywords = ['table', 'row', 'column', 'cell', '|', 'thead', 'tbody', 'tr', 'td']
            
            for i, chunk in enumerate(chunks):
                content = chunk.get('content', '').lower()
                
                # Check if chunk contains table-like content
                table_indicators = sum(1 for keyword in table_keywords if keyword in content)
                
                if table_indicators >= 2:  # At least 2 table indicators
                    table_chunks += 1
                    print(f"📊 Chunk {i}: Possible table content (indicators: {table_indicators})")
                    print(f"   Content preview: {chunk.get('content', '')[:200]}...")
                    print()
                
                # Show first few chunks regardless
                if i < 3:
                    print(f"📄 Chunk {i}:")
                    print(f"   Content: {chunk.get('content', '')[:300]}...")
                    print(f"   Length: {len(chunk.get('content', ''))}")
                    print()
            
            print(f"📊 ANALYSIS:")
            print(f"   Total chunks: {len(chunks)}")
            print(f"   Possible table chunks: {table_chunks}")
            
            if table_chunks == 0:
                print(f"   ⚠️  No table content detected in chunks")
                print(f"   🔍 This suggests table processing is not working")
            else:
                print(f"   ✅ Found {table_chunks} chunks with table-like content")
                
        else:
            print(f"❌ Failed to get chunks: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error checking chunks: {e}")

def check_dit_api_status():
    """Check if DiT API is working"""
    
    print(f"\n🔍 CHECKING DiT API STATUS")
    print("="*40)
    
    dit_api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
    
    try:
        response = requests.get(dit_api_url, timeout=10)
        print(f"✅ DiT API Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   🎯 DiT API is accessible")
            return True
        else:
            print(f"   ⚠️  DiT API returned: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DiT API Error: {e}")
        return False

def check_document_status():
    """Check the status of processed documents"""
    
    print(f"\n🔍 CHECKING DOCUMENT PROCESSING STATUS")
    print("="*50)
    
    # RAGFlow API settings
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    dataset_id = "7173e3a651d911f090a802420ae90806"
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Get documents in dataset
        docs_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/documents"
        response = requests.get(docs_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('data', {}).get('docs', [])
            
            print(f"📄 Found {len(documents)} documents")
            
            for doc in documents:
                doc_id = doc.get('id', 'unknown')
                name = doc.get('name', 'unknown')
                status = doc.get('status', 'unknown')
                progress = doc.get('progress', 0)
                chunk_num = doc.get('chunk_num', 0)
                
                print(f"   📄 {name}:")
                print(f"      ID: {doc_id}")
                print(f"      Status: {status}")
                print(f"      Progress: {progress}%")
                print(f"      Chunks: {chunk_num}")
                
                # Check parsing config
                parser_config = doc.get('parser_config', {})
                if parser_config:
                    layout_recognize = parser_config.get('layout_recognize', 'unknown')
                    chunk_method = parser_config.get('chunk_method', 'unknown')
                    print(f"      Layout Model: {layout_recognize}")
                    print(f"      Chunk Method: {chunk_method}")
                
                print()
                
        else:
            print(f"❌ Failed to get documents: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking documents: {e}")

def analyze_table_processing_failure():
    """Analyze why table processing might be failing"""
    
    print(f"\n🔍 ANALYZING TABLE PROCESSING FAILURE")
    print("="*50)
    
    print("🎯 POSSIBLE REASONS FOR TABLE PROCESSING FAILURE:")
    print()
    
    print("1. 📊 DiT Model Not Detecting Tables:")
    print("   - DiT API might not be classifying regions as 'TABLE'")
    print("   - Confidence threshold too high")
    print("   - PDFs don't contain clear table structures")
    print()
    
    print("2. 🔧 TableStructureRecognizer Issues:")
    print("   - Model files missing or corrupted")
    print("   - Table regions too small/large for processing")
    print("   - Image quality issues after cropping")
    print()
    
    print("3. ⚙️  Configuration Issues:")
    print("   - Wrong layout_recognize setting")
    print("   - Parser config not enabling table processing")
    print("   - Chunk method not preserving table structure")
    print()
    
    print("4. 🔗 Integration Issues:")
    print("   - DiT results not properly filtered")
    print("   - Table components not integrated into final output")
    print("   - Chunking process destroying table structure")
    print()
    
    print("🎯 DEBUGGING STEPS:")
    print("1. Check if DiT API is returning 'TABLE' classifications")
    print("2. Verify table regions are being cropped")
    print("3. Check if TableStructureRecognizer is processing cropped images")
    print("4. Verify table components are integrated into chunks")

def main():
    """Main diagnostic function"""
    
    print("🚨 TABLE PROCESSING FAILURE INVESTIGATION")
    print("="*80)
    print("You mentioned: 'table analysis is 0 second, and i have table but didnt get table result'")
    print("Let's investigate why table processing is failing...")
    print()
    
    # Check 1: DiT API status
    dit_working = check_dit_api_status()
    
    # Check 2: Document processing status
    check_document_status()
    
    # Check 3: Generated chunks
    check_ragflow_chunks()
    
    # Check 4: Analysis
    analyze_table_processing_failure()
    
    print(f"\n📋 INVESTIGATION SUMMARY")
    print("="*40)
    print(f"✅ DiT API: {'Working' if dit_working else 'Issues detected'}")
    print(f"📄 Documents: Check output above for processing status")
    print(f"🧩 Chunks: Check output above for table content")
    print()
    print("🎯 NEXT STEPS:")
    print("1. If DiT API is working but no table content in chunks:")
    print("   → DiT is not detecting tables in your PDFs")
    print("2. If chunks contain table-like content:")
    print("   → Table processing is working, check extraction method")
    print("3. If no table content at all:")
    print("   → Complete table processing pipeline failure")

if __name__ == "__main__":
    main()
