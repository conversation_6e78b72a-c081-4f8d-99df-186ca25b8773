#!/usr/bin/env python3
"""
Final test with optimal RAGFlow configuration based on default settings
"""

import os
import time
import json
from main_ragapi import RAGFlowAPIClient

def test_final_solution():
    """Test the final solution with optimal configuration"""
    print("🚀 Final Solution Test - Optimal RAGFlow Configuration")
    print("=" * 70)
    
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    DATASET_NAME = "testrag_pure_default"
    
    try:
        client = RAGFlowAPIClient(API_BASE_URL, API_KEY)
        
        # Step 1: Create dataset with optimal configuration
        print(f"1. 📦 Creating dataset with optimal configuration: {DATASET_NAME}")
        dataset_id = client.create_or_find_dataset(DATASET_NAME)
        
        if not dataset_id:
            print("❌ Failed to create dataset")
            return False
        
        print(f"   ✅ Dataset ready: {dataset_id}")
        
        # Step 2: Test with a PDF file
        PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
        if not os.path.exists(PDF_FOLDER):
            print(f"❌ PDF folder not found: {PDF_FOLDER}")
            return False
        
        pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.endswith('.pdf')]
        if not pdf_files:
            print(f"❌ No PDF files found in: {PDF_FOLDER}")
            return False
        
        # Use the first PDF file for testing
        test_pdf = pdf_files[0]
        test_pdf_path = os.path.join(PDF_FOLDER, test_pdf)
        
        print(f"\n2. 📄 Testing with PDF: {test_pdf}")
        
        # Step 3: Check for duplicates first
        print(f"3. 🔍 Checking for existing documents...")
        existing_doc_id = client.check_document_exists(dataset_id, test_pdf)
        
        if existing_doc_id:
            print(f"   ♻️ Document already exists: {existing_doc_id}")
            doc_id = existing_doc_id
        else:
            print(f"   📤 Uploading new document...")
            doc_id = client.upload_document(dataset_id, test_pdf_path)
            
            if not doc_id:
                print("   ❌ Upload failed")
                return False
            
            print(f"   ✅ Upload successful: {doc_id}")
        
        # Step 4: Parse the document
        print(f"4. 🔄 Initiating parsing with optimal configuration...")
        
        parse_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/chunks"
        parse_payload = {"document_ids": [doc_id]}
        
        try:
            parse_response = client.session.post(parse_url, json=parse_payload)

            if parse_response.status_code == 200:
                parse_result = parse_response.json()
                if parse_result.get("code") == 0:
                    print("   ✅ Parsing initiated successfully")
                else:
                    print(f"   ❌ Parsing failed: {parse_result.get('message')}")
                    print(f"   🔍 Full response: {parse_result}")
                    return False
            else:
                print(f"   ❌ Parsing HTTP error: {parse_response.status_code}")
                print(f"   🔍 Response text: {parse_response.text}")
                return False
        except Exception as e:
            print(f"   ❌ Parsing exception: {type(e).__name__}: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        # Step 5: Monitor parsing progress
        print(f"5. ⏳ Monitoring parsing progress...")
        
        for i in range(24):  # Check for 4 minutes (24 * 10 seconds)
            time.sleep(10)
            
            # Check document status
            status_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/documents"
            status_response = client.session.get(status_url)
            
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get("code") == 0:
                    docs_data = status_result.get("data", {})
                    docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data
                    
                    # Find our document
                    current_doc = None
                    for doc in docs_list:
                        if doc.get("id") == doc_id:
                            current_doc = doc
                            break
                    
                    if current_doc:
                        status = current_doc.get('run', 'unknown')
                        progress = current_doc.get('progress', 0)
                        chunks = current_doc.get('chunk_count', 0)
                        progress_msg = current_doc.get('progress_msg', '')
                        
                        print(f"   📊 Status: {status}, Progress: {progress:.1%}, Chunks: {chunks}")
                        
                        if progress_msg:
                            # Show only the last line of progress message
                            last_line = progress_msg.strip().split('\n')[-1]
                            print(f"   💬 Latest: {last_line}")
                        
                        # Check if processing completed successfully
                        if status in ['FINISH', 'DONE']:
                            print(f"   🎉 Processing completed successfully! Generated {chunks} chunks")
                            
                            # Step 6: Retrieve chunks
                            print(f"6. 📥 Retrieving chunks...")
                            retrieved_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                            
                            if retrieved_chunks:
                                print(f"   ✅ Successfully retrieved {len(retrieved_chunks)} chunks!")
                                
                                # Save results
                                output_file = f"final_solution_chunks_{DATASET_NAME}.json"
                                output_data = {
                                    "dataset_name": DATASET_NAME,
                                    "dataset_id": dataset_id,
                                    "document_name": test_pdf,
                                    "document_id": doc_id,
                                    "total_chunks": len(retrieved_chunks),
                                    "processing_status": "SUCCESS",
                                    "configuration_used": "optimal_based_on_defaults",
                                    "chunks": retrieved_chunks
                                }
                                
                                with open(output_file, 'w', encoding='utf-8') as f:
                                    json.dump(output_data, f, indent=2, ensure_ascii=False)
                                
                                print(f"   💾 Results saved to: {output_file}")
                                
                                # Show sample content
                                if retrieved_chunks and retrieved_chunks[0].get('content'):
                                    sample = retrieved_chunks[0]['content'][:200]
                                    print(f"   📄 Sample chunk: '{sample}{'...' if len(retrieved_chunks[0]['content']) > 200 else ''}'")
                                
                                return True
                            else:
                                print(f"   ❌ Failed to retrieve chunks")
                                return False
                        
                        # Check if processing failed
                        elif status == 'FAIL':
                            print(f"   ❌ Processing failed!")
                            if progress_msg:
                                print(f"   💬 Error details: {progress_msg}")
                            return False
            
            print(f"   ⏳ Still processing... ({(i+1)*10}s elapsed)")
        
        print("   ⏰ Timeout reached - processing may still be in progress")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Final Solution Test")
    print("=" * 50)
    print("✅ Features being tested:")
    print("   - Optimal RAGFlow configuration based on defaults")
    print("   - Proper filename_embd_weight parameter (fixes embedding error)")
    print("   - Duplicate file checking")
    print("   - Complete processing pipeline")
    print("   - Chunk retrieval and storage")
    print()
    
    success = test_final_solution()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 FINAL SOLUTION TEST PASSED!")
        print("\n✅ Confirmed working:")
        print("   - Dataset creation with optimal configuration")
        print("   - Duplicate file detection and handling")
        print("   - Document upload and parsing")
        print("   - Complete processing pipeline (OCR → Layout → Chunking → Embedding)")
        print("   - Successful chunk retrieval")
        print("   - JSON output generation")
        print("\n🚀 The complete RAGFlow workflow is now functional!")
        print("   You can now use main_ragapi.py to process your PDF folder.")
    else:
        print("❌ Final solution test failed")
        print("\n🔍 Check the logs above for specific error details")
    
    print(f"\n📝 Next steps:")
    print("   1. If successful: Run main_ragapi.py on your PDF folder")
    print("   2. If failed: Review error messages and adjust configuration")
    print("   3. Check generated JSON files for chunk content")

if __name__ == "__main__":
    main()
