#!/usr/bin/env python3
"""
RAGFlow API-based Document Processing Pipeline (Optimized)
Processes multiple PDF files using RAGFlow's REST API
"""

import os
import sys
import json
import time
import requests
import glob
from datetime import datetime
from requests_toolbelt.multipart.encoder import MultipartEncoder

class RAGFlowAPIClient:
    """Optimized RAGFlow API client"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir):
        """Save output from each processing step"""
        filename = f"{step_name}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"  💾 Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"  ⚠️  Failed to save {step_name} output: {e}")
            return None
    
    def create_dataset(self, dataset_name):
        """Step 1: Create a dataset"""
        print(f"1. Creating dataset: {dataset_name}")
        url = f"{self.api_base_url}/api/v1/datasets"
        
        payload = {
            "name": dataset_name,
            "description": f"Dataset for {dataset_name}",
            "chunk_method": "naive",
            "parser_config": {
                "chunk_token_num": 512,
                "layout_recognize": "DeepDOC"
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"  ✅ Dataset created: {dataset_id}")
                return dataset_id
            else:
                print(f"  ❌ Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  ❌ Request failed: {str(e)}")
            return None
    
    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF document"""
        print(f"2. Uploading: {os.path.basename(pdf_path)}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        
        try:
            # Correct multipart upload using 'files' parameter
            multipart_data = MultipartEncoder(
                fields={'files': (os.path.basename(pdf_path), open(pdf_path, 'rb'), 'application/pdf')}
            )
            
            response = self.session.post(
                url,
                data=multipart_data,
                headers={'Content-Type': multipart_data.content_type}
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                document_id = result["data"]["documents"][0]["id"]
                print(f"  ✅ Document uploaded: {document_id}")
                return document_id
            else:
                print(f"  ❌ Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  ❌ Upload failed: {str(e)}")
            return None
    
    def parse_documents(self, dataset_id, document_id):
        """Step 3: Parse uploaded documents"""
        print(f"3. Parsing document: {document_id}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        payload = {"document_ids": [document_id]}
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                print("  ✅ Parsing initiated")
                return True
            else:
                print(f"  ❌ Failed: {result.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            print(f"  ❌ Parse request failed: {str(e)}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, timeout=600, interval=10):
        """Step 4: Wait for parsing completion"""
        print(f"4. Waiting for completion (timeout: {timeout}s)...")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                response.raise_for_status()
                doc_data = response.json().get("data", {})
                
                status = doc_data.get("status", "unknown")
                progress = doc_data.get("progress", 0)
                
                print(f"  📊 Status: {status}, Progress: {progress:.0%}")
                
                if status == "indexed":
                    print("  ✅ Parsing completed!")
                    return True, doc_data
                elif status == "error":
                    print(f"  ❌ Parsing failed: {doc_data.get('progress_msg', 'Unknown error')}")
                    return False, doc_data
                
                time.sleep(interval)
            except Exception as e:
                print(f"  ⚠️ Status check failed: {str(e)}")
                time.sleep(interval)
        
        print(f"  ⏰ Timeout after {timeout}s")
        return False, None
    
    def retrieve_chunks(self, dataset_id, document_id, page_size=500):
        """Step 5: Retrieve processed chunks"""
        print(f"5. Retrieving chunks for document: {document_id}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        params = {
            "document_id": document_id,  # Singular parameter name
            "page_size": page_size
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                chunks = result["data"].get("chunks", [])
                print(f"  ✅ Retrieved {len(chunks)} chunks")
                return chunks
            else:
                print(f"  ❌ Failed: {result.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            print(f"  ❌ Retrieve failed: {str(e)}")
            return []

def process_pdf_via_api(pdf_path, output_path, api_base_url, api_key):
    """Process a PDF file using RAGFlow API"""
    client = RAGFlowAPIClient(api_base_url, api_key)
    filename = os.path.basename(pdf_path)
    output_dir = client.create_output_directory(output_path, filename)
    
    # Generate dataset name
    dataset_name = f"{os.path.splitext(filename)[0]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Step 1: Create dataset
        dataset_id = client.create_dataset(dataset_name)
        if not dataset_id:
            return []
        client.save_step_output({"dataset_id": dataset_id}, "01_dataset", output_dir)
        
        # Step 2: Upload document
        doc_id = client.upload_document(dataset_id, pdf_path)
        if not doc_id:
            return []
        client.save_step_output({"document_id": doc_id}, "02_upload", output_dir)
        
        # Step 3: Parse document
        if not client.parse_documents(dataset_id, doc_id):
            return []
        client.save_step_output({"parse_initiated": True}, "03_parse_init", output_dir)
        
        # Step 4: Wait for completion
        success, doc_info = client.wait_for_parsing_completion(dataset_id, doc_id)
        client.save_step_output(doc_info or {}, "04_parse_status", output_dir)
        if not success:
            return []
        
        # Step 5: Retrieve chunks
        chunks = client.retrieve_chunks(dataset_id, doc_id)
        client.save_step_output({"chunks": chunks}, "05_chunks", output_dir)
        
        # Save readable version
        readable = [{
            "id": idx + 1,
            "content": c.get("content", ""),
            "keywords": c.get("important_keywords", []),
            "page": c.get("position_info", {}).get("page", 0)
        } for idx, c in enumerate(chunks)]
        
        client.save_step_output(readable, "06_readable_chunks", output_dir)
        
        print(f"\n✅ Processing completed! Chunks: {len(chunks)}")
        print(f"📁 Output: {output_dir}")
        return chunks
        
    except Exception as e:
        print(f"❌ Processing failed: {str(e)}")
        return []

def main():
    """Main function"""

    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "your_api_key_here")
    
    PDF_FOLDER = r"/eoaasvm004/ml/shared/lazhang/data/pdfdata"
    OUTPUT_PATH = r"/ml/shared/lazhang/data"
    
    print("🚀 Starting RAGFlow PDF Processing")
    print("=" * 50)
    
    # Process all PDFs in folder
    pdf_files = glob.glob(os.path.join(PDF_FOLDER, "*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    print(f"📄 Found {len(pdf_files)} PDFs")
    
    for i, pdf_path in enumerate(pdf_files, 1):
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {os.path.basename(pdf_path)}")
        process_pdf_via_api(pdf_path, OUTPUT_PATH, API_BASE_URL, API_KEY)
    
    print("\n🎉 All processing completed!")

if __name__ == "__main__":
    main()