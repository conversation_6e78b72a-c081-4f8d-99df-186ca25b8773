#!/usr/bin/env python3
"""
Test script to verify duplicate file checking is working correctly
"""

import os
import sys
from main_ragapi import RAGFlowAPIClient

def test_duplicate_checking():
    """Test the duplicate file checking functionality"""
    print("🧪 Testing Duplicate File Checking")
    print("=" * 50)
    
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    DATASET_NAME = "testrag_manual"
    
    try:
        client = RAGFlowAPIClient(API_BASE_URL, API_KEY)
        
        # Step 1: Find or create the test dataset
        print(f"1. 📦 Finding dataset: {DATASET_NAME}")
        dataset_id = client.create_or_find_dataset(DATASET_NAME)
        
        if not dataset_id:
            print("❌ Failed to find/create dataset")
            return False
        
        print(f"   ✅ Dataset found: {dataset_id}")
        
        # Step 2: List all documents in the dataset
        print(f"\n2. 📄 Listing all documents in dataset...")
        url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        response = client.session.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                data = result.get("data", [])
                # Handle different response formats
                if isinstance(data, list):
                    documents = data
                elif isinstance(data, dict):
                    documents = data.get("docs", [])
                else:
                    documents = []
                
                print(f"   📊 Found {len(documents)} documents in dataset:")
                
                if documents:
                    for i, doc in enumerate(documents, 1):
                        name = doc.get("name", "Unknown")
                        doc_id = doc.get("id", "Unknown")
                        status = doc.get("run", "Unknown")
                        chunks = doc.get("chunk_count", 0)
                        print(f"      {i}. {name} (ID: {doc_id})")
                        print(f"         Status: {status}, Chunks: {chunks}")
                else:
                    print("      (No documents found)")
                
                # Step 3: Test duplicate checking for each document
                print(f"\n3. 🔍 Testing duplicate checking...")
                
                test_files = ["g2mod.pdf", "g2mod(1).pdf", "nonexistent.pdf"]
                
                for test_file in test_files:
                    print(f"\n   Testing file: {test_file}")
                    existing_id = client.check_document_exists(dataset_id, test_file)
                    
                    if existing_id:
                        print(f"   ✅ Duplicate check PASSED - Found existing: {existing_id}")
                    else:
                        print(f"   ❌ Duplicate check result - Not found (would upload)")
                
                return True
            else:
                print(f"   ❌ API error: {result.get('message')}")
                return False
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Duplicate File Checking Test")
    print("=" * 60)
    
    success = test_duplicate_checking()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Duplicate checking test completed!")
        print("\n💡 Key points:")
        print("   - The test shows which files already exist in the dataset")
        print("   - Files like 'g2mod.pdf' and 'g2mod(1).pdf' should be detected as duplicates")
        print("   - The improved logic should prevent re-uploading existing files")
    else:
        print("❌ Test failed - check the logs above")
    
    print(f"\n📝 Next steps:")
    print("   1. Review the document list above")
    print("   2. Run the main processing script")
    print("   3. Verify no duplicate uploads occur")

if __name__ == "__main__":
    main()
