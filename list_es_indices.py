#!/usr/bin/env python3
"""
List Elasticsearch indices to help find the correct index name.
"""

import os
from elasticsearch import Elasticsearch


os.environ["REQUESTS_CA_BUNDLE"] = "/etc/ssl/certs/ca-certificates.crt"
os.environ["CURL_CA_BUNDLE"] = "/etc/ssl/certs/ca-certificates.crt"
os.environ["SSL_CERT_FILE"] = "/etc/ssl/certs/ca-certificates.crt"

def list_indices():
    """Connect to Elasticsearch and list all indices."""
    try:
        # Connect to Elasticsearch with your configuration
        es_client = Elasticsearch(
            "http://eoaagmld007:1200",
            basic_auth=("elastic", "infini_rag_flow")
        )
        
        # Test connection
        if not es_client.ping():
            print("Could not connect to Elasticsearch")
            return
        
        print("Connected to Elasticsearch successfully!")
        print("\nAvailable indices:")
        print("-" * 50)
        
        # Get all indices
        indices = es_client.cat.indices(format="json")
        
        if not indices:
            print("No indices found")
            return
        
        # Sort by index name for better readability
        indices = sorted(indices, key=lambda x: x['index'])
        
        # Print indices with details
        print(f"{'Index Name':<30} {'Status':<10} {'Docs Count':<12} {'Store Size':<12}")
        print("-" * 70)
        
        for index in indices:
            index_name = index.get('index', 'N/A')
            status = index.get('status', 'N/A')
            docs_count = index.get('docs.count', 'N/A')
            store_size = index.get('store.size', 'N/A')
            
            print(f"{index_name:<30} {status:<10} {docs_count:<12} {store_size:<12}")
        
        print(f"\nTotal indices: {len(indices)}")
        
        # Look for likely document indices (containing common patterns)
        likely_indices = []
        for index in indices:
            index_name = index['index'].lower()
            if any(pattern in index_name for pattern in ['doc', 'chunk', 'content', 'text', 'rag']):
                likely_indices.append(index['index'])
        
        if likely_indices:
            print(f"\nLikely document/chunk indices:")
            for idx in likely_indices:
                print(f"  - {idx}")
        
    except Exception as e:
        print(f"Error connecting to Elasticsearch: {e}")
        print("Make sure Elasticsearch is running on localhost:1200")

if __name__ == "__main__":
    list_indices()
