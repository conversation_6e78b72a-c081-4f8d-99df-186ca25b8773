#!/usr/bin/env python3
"""
Test Table Processing Fix
This script tests the table processing with DiT configuration directly
"""

import os
import sys
import json
import subprocess
import time

def test_with_table_pdf():
    """Test processing with the obvious table PDF we created"""
    
    print("🔍 TESTING TABLE PROCESSING WITH OBVIOUS TABLE PDF")
    print("="*60)
    
    test_pdf = "/tmp/table_test_obvious.pdf"
    
    if not os.path.exists(test_pdf):
        print(f"❌ Test PDF not found: {test_pdf}")
        return False
    
    print(f"📄 Test PDF: {test_pdf}")
    print(f"📊 Contains: 3 tables with clear structure")
    
    # Copy to data directory for processing
    data_dir = "/ml/shared/lazhang/data/pdfdata"
    if os.path.exists(data_dir):
        import shutil
        dest_path = os.path.join(data_dir, "table_test_obvious.pdf")
        shutil.copy2(test_pdf, dest_path)
        print(f"✅ Copied to: {dest_path}")
        
        # Now process with our main script using DiT configuration
        print(f"\n🚀 PROCESSING WITH MODIFIED CONFIGURATION...")
        return True
    else:
        print(f"❌ Data directory not found: {data_dir}")
        return False

def create_dit_processing_script():
    """Create a modified processing script that forces DiT usage"""
    
    print(f"\n🔧 CREATING DiT-FORCED PROCESSING SCRIPT")
    print("="*50)
    
    script_content = '''#!/usr/bin/env python3
"""
Modified RAGFlow processing script that forces DiT usage
This bypasses the configuration issue by directly specifying DiT
"""

import requests
import json
import time
import os

def process_with_dit_config():
    """Process PDFs with DiT configuration forced"""
    
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Create a new dataset with DiT configuration
    dataset_payload = {
        "name": "table_test_dit",
        "description": "Testing table processing with DiT model",
        "language": "English",
        "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI",
        "chunk_method": "naive",
        "parser_config": {
            "chunk_token_num": 128,
            "delimiter": "\\n",
            "html4excel": False,
            "layout_recognize": "DiT",  # Force DiT usage
            "raptor": {"use_raptor": False},
            "filename_embd_weight": 0.1  # Prevent embedding errors
        }
    }
    
    try:
        # Create dataset
        create_url = f"{api_base_url}/api/v1/datasets"
        response = requests.post(create_url, headers=headers, json=dataset_payload)
        
        if response.status_code == 200:
            dataset_data = response.json()
            dataset_id = dataset_data.get('data', {}).get('dataset_id')
            print(f"✅ Created DiT dataset: {dataset_id}")
            
            # Upload test PDF
            pdf_path = "/ml/shared/lazhang/data/pdfdata/table_test_obvious.pdf"
            if os.path.exists(pdf_path):
                print(f"📤 Uploading: {pdf_path}")
                
                # Upload file
                with open(pdf_path, 'rb') as f:
                    files = {'file': ('table_test_obvious.pdf', f, 'application/pdf')}
                    upload_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/documents"
                    
                    upload_response = requests.post(upload_url, headers={'Authorization': f'Bearer {api_key}'}, files=files)
                    
                    if upload_response.status_code == 200:
                        doc_data = upload_response.json()
                        doc_id = doc_data.get('data', {}).get('document_id')
                        print(f"✅ Uploaded document: {doc_id}")
                        
                        # Parse with DiT
                        parse_payload = {
                            "document_ids": [doc_id]
                        }
                        
                        parse_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/chunks"
                        parse_response = requests.post(parse_url, headers=headers, json=parse_payload)
                        
                        if parse_response.status_code == 200:
                            print(f"✅ Parsing initiated with DiT configuration")
                            print(f"🔄 Monitor Docker logs for table processing...")
                            return True
                        else:
                            print(f"❌ Parse failed: {parse_response.status_code}")
                    else:
                        print(f"❌ Upload failed: {upload_response.status_code}")
            else:
                print(f"❌ Test PDF not found: {pdf_path}")
        else:
            print(f"❌ Dataset creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return False

if __name__ == "__main__":
    print("🚀 PROCESSING WITH DiT CONFIGURATION")
    print("="*50)
    process_with_dit_config()
'''
    
    with open("process_with_dit.py", "w") as f:
        f.write(script_content)
    
    print("✅ Created process_with_dit.py")
    print("💡 This script creates a new dataset with DiT configuration")

def monitor_docker_logs():
    """Monitor Docker logs for table processing"""
    
    print(f"\n📊 MONITORING DOCKER LOGS FOR TABLE PROCESSING")
    print("="*55)
    
    print("🔍 Run this command in another terminal to monitor:")
    print("   docker logs -f ragflow-server | grep -E '(Table analysis|Layout analysis|DiT|table)'")
    print()
    print("🎯 WHAT TO LOOK FOR:")
    print("   ✅ 'Table analysis (X.XXs)' where X > 0.00")
    print("   ✅ DiT-related debug output (if our logging works)")
    print("   ✅ Increased processing time for table analysis")
    print()
    print("❌ CURRENT ISSUE:")
    print("   ⚠️  'Table analysis (0.00s)' = no table processing")

def provide_testing_instructions():
    """Provide clear testing instructions"""
    
    print(f"\n🎯 TESTING INSTRUCTIONS")
    print("="*30)
    
    print("📋 STEP-BY-STEP TEST:")
    print()
    
    print("1. 🔍 MONITOR LOGS (in separate terminal):")
    print("   docker logs -f ragflow-server")
    print()
    
    print("2. 🚀 RUN DiT PROCESSING:")
    print("   python process_with_dit.py")
    print()
    
    print("3. 👀 WATCH FOR CHANGES:")
    print("   - Look for 'Table analysis (X.XXs)' where X > 0")
    print("   - Compare with previous '0.00s' times")
    print("   - Check if processing takes longer")
    print()
    
    print("4. ✅ SUCCESS INDICATORS:")
    print("   - Table analysis > 0.00s")
    print("   - Higher chunk count for table PDF")
    print("   - DiT debug output (if visible)")
    print()
    
    print("5. 🔄 IF STILL 0.00s:")
    print("   - DiT may not be properly configured")
    print("   - Need to check RAGFlow DiT integration")
    print("   - May need server-side configuration changes")

def main():
    """Main testing function"""
    
    print("🧪 TABLE PROCESSING FIX TESTING")
    print("="*50)
    print("Testing the DiT configuration fix")
    print()
    
    # Step 1: Prepare test PDF
    pdf_ready = test_with_table_pdf()
    
    # Step 2: Create DiT processing script
    create_dit_processing_script()
    
    # Step 3: Provide monitoring instructions
    monitor_docker_logs()
    
    # Step 4: Provide testing instructions
    provide_testing_instructions()
    
    print(f"\n📋 SUMMARY")
    print("="*20)
    print(f"📄 Test PDF: {'✅ Ready' if pdf_ready else '❌ Not ready'}")
    print(f"🔧 DiT Script: ✅ Created")
    print(f"📊 Monitoring: ✅ Instructions provided")
    print()
    
    if pdf_ready:
        print(f"🚀 READY TO TEST!")
        print(f"   1. Open new terminal: docker logs -f ragflow-server")
        print(f"   2. Run: python process_with_dit.py")
        print(f"   3. Watch for table analysis time > 0.00s")
    else:
        print(f"⚠️  Fix test PDF setup first")

if __name__ == "__main__":
    main()
