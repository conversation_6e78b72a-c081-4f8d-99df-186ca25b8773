#!/usr/bin/env python3
"""
Test script to isolate the parsing issue and avoid embedding errors
"""

import os
import sys
import time
from main_ragapi import RAGFlowAPIClient

def test_parsing_without_embedding():
    """Test parsing with minimal configuration to avoid embedding issues"""
    print("🧪 Testing Parsing Without Embedding Issues")
    print("=" * 60)
    
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    DATASET_NAME = "testrag_simple"  # Use a new dataset name
    
    try:
        client = RAGFlowAPIClient(API_BASE_URL, API_KEY)
        
        # Step 1: Create a new simple dataset
        print(f"1. 📦 Creating simple dataset: {DATASET_NAME}")
        
        url = f"{API_BASE_URL}/api/v1/datasets"
        
        # Ultra-simple payload to avoid any embedding configuration issues
        payload = {
            "name": DATASET_NAME,
            "description": f"Simple test dataset for {DATASET_NAME}",
            "chunk_method": "manual",
            "parser_config": {
                "chunk_token_num": 256,  # Smaller chunks
                "layout_recognize": "manual",
                "delimiter": "\n"
            }
        }
        
        response = client.session.post(url, json=payload)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"   ✅ Simple dataset created: {dataset_id}")
            else:
                print(f"   ❌ Dataset creation failed: {result.get('message')}")
                return False
        else:
            print(f"   ❌ HTTP error creating dataset: {response.status_code}")
            return False
        
        # Step 2: Check if we have any PDF files to test with
        PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
        if not os.path.exists(PDF_FOLDER):
            print(f"❌ PDF folder not found: {PDF_FOLDER}")
            return False
        
        pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.endswith('.pdf')]
        if not pdf_files:
            print(f"❌ No PDF files found in: {PDF_FOLDER}")
            return False
        
        # Use the first PDF file for testing
        test_pdf = pdf_files[0]
        test_pdf_path = os.path.join(PDF_FOLDER, test_pdf)
        
        print(f"\n2. 📄 Testing with PDF: {test_pdf}")
        
        # Step 3: Upload the test PDF
        print(f"3. 📤 Uploading test PDF...")
        doc_id = client.upload_document(dataset_id, test_pdf_path)
        
        if not doc_id:
            print("   ❌ Upload failed")
            return False
        
        print(f"   ✅ Upload successful: {doc_id}")
        
        # Step 4: Try parsing with minimal configuration
        print(f"4. 🔄 Initiating parsing (without embedding)...")
        
        parse_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/chunks"
        parse_payload = {
            "document_ids": [doc_id]
        }
        
        parse_response = client.session.post(parse_url, json=parse_payload)
        
        if parse_response.status_code == 200:
            parse_result = parse_response.json()
            if parse_result.get("code") == 0:
                print("   ✅ Parsing initiated successfully")
            else:
                print(f"   ❌ Parsing failed: {parse_result.get('message')}")
                return False
        else:
            print(f"   ❌ Parsing HTTP error: {parse_response.status_code}")
            return False
        
        # Step 5: Monitor parsing progress
        print(f"5. ⏳ Monitoring parsing progress...")
        
        for i in range(12):  # Check for 2 minutes (12 * 10 seconds)
            time.sleep(10)
            
            # Check document status
            status_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/documents"
            status_response = client.session.get(status_url)
            
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get("code") == 0:
                    docs_data = status_result.get("data", {})
                    docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data
                    
                    if docs_list:
                        doc = docs_list[0]  # Get our test document
                        status = doc.get('run', 'unknown')
                        progress = doc.get('progress', 0)
                        chunks = doc.get('chunk_count', 0)
                        progress_msg = doc.get('progress_msg', '')
                        
                        print(f"   📊 Status: {status}, Progress: {progress:.1%}, Chunks: {chunks}")
                        
                        if progress_msg:
                            print(f"   💬 Message: {progress_msg}")
                        
                        if status == 'FINISH':
                            print(f"   🎉 Parsing completed successfully! Generated {chunks} chunks")
                            return True
                        elif status == 'FAIL':
                            print(f"   ❌ Parsing failed: {progress_msg}")
                            return False
            
            print(f"   ⏳ Still processing... ({(i+1)*10}s elapsed)")
        
        print("   ⏰ Timeout reached - parsing may still be in progress")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Simple Parsing Test (No Embedding)")
    print("=" * 70)
    
    success = test_parsing_without_embedding()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Simple parsing test PASSED!")
        print("\n✅ This means:")
        print("   - Basic parsing functionality works")
        print("   - The issue might be with embedding configuration")
        print("   - We can proceed with parsing-only workflow")
    else:
        print("❌ Simple parsing test FAILED")
        print("\n🔍 This suggests:")
        print("   - There may be a fundamental parsing issue")
        print("   - Check document format compatibility")
        print("   - Review RAGFlow server configuration")
    
    print(f"\n📝 Recommendations:")
    print("   1. If test passed: Use parsing without embedding")
    print("   2. If test failed: Check RAGFlow server logs")
    print("   3. Consider using different parser configuration")

if __name__ == "__main__":
    main()
