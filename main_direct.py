#!/usr/bin/env python3
"""
Direct RAGFlow Document Processing Pipeline
Processes PDF files using RAGFlow's internal functions directly:
1. Parse PDFs using internal parsers
2. Chunk content using internal chunking
3. Generate embeddings using internal models
4. Store directly in Elasticsearch
5. Extract chunks for analysis
"""

import os
import sys
import glob
import json
import copy
from pathlib import Path
from uuid import uuid4
import hashlib
from datetime import datetime

# RAGFlow Internal Imports
from deepdoc.parser import PdfParser
from rag.nlp import tokenize_chunks, rag_tokenizer, naive_merge
from rag.nlp.rag_tokenizer import num_tokens_from_string
from rag.utils.es_conn import ESConnection
from rag.llm.embedding_model import DefaultEmbedding
from api import settings

def get_uuid():
    """Generate UUID for documents and chunks"""
    return str(uuid4()).replace("-", "")

def create_output_directory(base_output_path, pdf_filename):
    """Create timestamped output directory for saving step outputs"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    pdf_name = os.path.splitext(pdf_filename)[0]
    output_dir = os.path.join(base_output_path, f"{pdf_name}_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_step_output(data, step_name, output_dir, filename_prefix=""):
    """Save output from each processing step"""
    try:
        if filename_prefix:
            filename = f"{filename_prefix}_{step_name}.json"
        else:
            filename = f"{step_name}.json"

        filepath = os.path.join(output_dir, filename)

        # Convert data to JSON-serializable format
        if isinstance(data, list):
            json_data = []
            for item in data:
                if hasattr(item, '__dict__'):
                    json_data.append(vars(item))
                elif isinstance(item, (dict, list, str, int, float, bool, type(None))):
                    json_data.append(item)
                else:
                    json_data.append(str(item))
        elif isinstance(data, dict):
            json_data = data
        else:
            json_data = {"data": str(data)}

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)

        print(f"  💾 Saved {step_name} output to: {filepath}")
        return filepath
    except Exception as e:
        print(f"  ⚠️  Failed to save {step_name} output: {e}")
        return None

def setup_elasticsearch():
    """Setup Elasticsearch connection using RAGFlow's internal connection"""
    print("🔧 Setting up Elasticsearch connection...")
    try:
        es_conn = ESConnection()
        print("✅ Elasticsearch connection established")
        return es_conn
    except Exception as e:
        print(f"❌ Failed to connect to Elasticsearch: {e}")
        sys.exit(1)

def setup_embedding_model():
    """Setup embedding model using RAGFlow's internal model"""
    print("🧠 Setting up embedding model...")
    try:
        # Use RAGFlow's default embedding model
        embedding_model = DefaultEmbedding()
        print("✅ Embedding model loaded")
        return embedding_model
    except Exception as e:
        print(f"❌ Failed to load embedding model: {e}")
        sys.exit(1)

def parse_pdf_document(pdf_path, output_dir=None):
    """Parse a single PDF document using RAGFlow's internal PDF parser"""
    print(f"1. Parsing PDF: {os.path.basename(pdf_path)}")

    try:
        # Initialize PDF parser
        pdf_parser = PdfParser()

        # Parse the PDF
        with open(pdf_path, 'rb') as f:
            pdf_binary = f.read()

        # Extract text and tables
        sections, tables = pdf_parser(pdf_binary, need_image=False, zoomin=3)

        print(f"  ✅ Extracted {len(sections)} sections and {len(tables)} tables")

        # Save parsing output if output directory provided
        if output_dir:
            parsing_output = {
                "pdf_path": pdf_path,
                "sections_count": len(sections),
                "tables_count": len(tables),
                "sections": [str(section) for section in sections],
                "tables": [str(table) for table in tables]
            }
            save_step_output(parsing_output, "01_pdf_parsing", output_dir)

        return sections, tables, pdf_parser

    except Exception as e:
        print(f"  ❌ Failed to parse PDF: {e}")
        return [], [], None

def chunk_content(sections, tables, filename, chunk_token_num=512, output_dir=None):
    """Chunk the parsed content using RAGFlow's internal chunking"""
    print(f"2. Chunking content from {filename}")

    try:
        # Prepare document metadata
        doc_base = {
            "docnm_kwd": filename,
            "title_tks": rag_tokenizer.tokenize(filename.replace('.pdf', '')),
            "doc_id": get_uuid(),
            "kb_id": get_uuid(),  # Generate a KB ID
            "create_time": "2025-06-20 00:00:00",
            "create_timestamp_flt": 1704067200.0
        }
        doc_base["title_sm_tks"] = rag_tokenizer.fine_grained_tokenize(doc_base["title_tks"])

        # Convert sections to text
        text_sections = []
        for section in sections:
            if isinstance(section, tuple):
                text_sections.append((section[0], ""))
            else:
                text_sections.append((str(section), ""))

        # Chunk the text using naive merge
        chunks = naive_merge(
            text_sections,
            chunk_token_num=chunk_token_num,
            delimiter="\n。；！？"
        )

        print(f"  ✅ Created {len(chunks)} chunks")

        # Save chunking output if output directory provided
        if output_dir:
            chunking_output = {
                "filename": filename,
                "chunk_token_num": chunk_token_num,
                "chunks_count": len(chunks),
                "doc_base": doc_base,
                "chunks": [{"content": str(chunk), "length": len(str(chunk))} for chunk in chunks]
            }
            save_step_output(chunking_output, "02_content_chunking", output_dir)

        return chunks, doc_base

    except Exception as e:
        print(f"  ❌ Failed to chunk content: {e}")
        return [], {}

def tokenize_and_prepare_chunks(chunks, doc_base, pdf_parser=None, output_dir=None):
    """Tokenize chunks and prepare them for Elasticsearch"""
    print("3. Tokenizing chunks...")

    try:
        # Use RAGFlow's tokenize_chunks function
        tokenized_chunks = tokenize_chunks(chunks, doc_base, eng=True, pdf_parser=pdf_parser)

        # Add required fields for Elasticsearch
        for i, chunk in enumerate(tokenized_chunks):
            chunk["id"] = hashlib.md5((chunk.get("content_with_weight", "") + str(i)).encode()).hexdigest()
            chunk["available"] = True
            chunk["document_id"] = doc_base["doc_id"]
            chunk["image_id"] = ""

            # Ensure all required fields exist
            if "important_keywords" not in chunk:
                chunk["important_keywords"] = []
            if "positions" not in chunk:
                chunk["positions"] = [[i, 0, 0, 100, 100]]  # Default position

        print(f"  ✅ Tokenized {len(tokenized_chunks)} chunks")

        # Save tokenization output if output directory provided
        if output_dir:
            tokenization_output = {
                "tokenized_chunks_count": len(tokenized_chunks),
                "doc_base": doc_base,
                "sample_chunk_keys": list(tokenized_chunks[0].keys()) if tokenized_chunks else [],
                "tokenized_chunks": tokenized_chunks
            }
            save_step_output(tokenization_output, "03_tokenization", output_dir)

        return tokenized_chunks

    except Exception as e:
        print(f"  ❌ Failed to tokenize chunks: {e}")
        return []

def generate_embeddings(chunks, embedding_model, output_dir=None):
    """Generate embeddings for chunks"""
    print("4. Generating embeddings...")

    try:
        # Extract content for embedding
        texts = [chunk.get("content_with_weight", "") for chunk in chunks]

        # Generate embeddings
        embeddings, token_count = embedding_model.encode(texts)

        # Add embeddings to chunks
        vector_keys = []
        for i, chunk in enumerate(chunks):
            if i < len(embeddings):
                vector_key = f"q_{len(embeddings[i])}_vec"
                chunk[vector_key] = embeddings[i].tolist()
                vector_keys.append(vector_key)

        print(f"  ✅ Generated embeddings for {len(chunks)} chunks")

        # Save embedding output if output directory provided
        if output_dir:
            embedding_output = {
                "chunks_count": len(chunks),
                "embeddings_count": len(embeddings),
                "token_count": token_count,
                "vector_dimension": len(embeddings[0]) if embeddings else 0,
                "vector_keys": vector_keys,
                "sample_embedding": embeddings[0].tolist() if embeddings else None
            }
            save_step_output(embedding_output, "04_embedding_generation", output_dir)

        return chunks

    except Exception as e:
        print(f"  ❌ Failed to generate embeddings: {e}")
        return chunks

def store_in_elasticsearch(chunks, es_conn, index_name, output_dir=None):
    """Store chunks in Elasticsearch"""
    print("5. Storing chunks in Elasticsearch...")

    try:
        # Create index if it doesn't exist
        kb_id = chunks[0]["kb_id"] if chunks else "default"
        vector_size = 384  # Default vector size

        if not es_conn.indexExist(index_name, kb_id):
            es_conn.createIdx(index_name, kb_id, vector_size)
            print(f"  📊 Created Elasticsearch index: {index_name}")

        # Insert chunks in batches
        batch_size = 10
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            es_conn.insert(batch, index_name, kb_id)

        print(f"  ✅ Stored {len(chunks)} chunks in Elasticsearch")

        # Save storage output if output directory provided
        if output_dir:
            storage_output = {
                "index_name": index_name,
                "kb_id": kb_id,
                "vector_size": vector_size,
                "chunks_stored": len(chunks),
                "batch_size": batch_size,
                "storage_timestamp": datetime.now().isoformat()
            }
            save_step_output(storage_output, "05_elasticsearch_storage", output_dir)

        return True

    except Exception as e:
        print(f"  ❌ Failed to store in Elasticsearch: {e}")
        return False

def extract_chunks_from_es(es_conn, index_name, output_dir):
    """Extract chunks from Elasticsearch and save to files"""
    print("🔍 Extracting chunks from Elasticsearch...")

    try:
        # Try to import extraction functions, if not available, create basic extraction
        try:
            sys.path.append('tools')
            from extract_es_chunks import extract_chunks, save_chunks
            chunks = extract_chunks(es_conn.es, index_name, max_chunks=1000)
        except ImportError:
            print("  ⚠️  extract_es_chunks not found, using basic extraction...")
            # Basic extraction using Elasticsearch directly
            try:
                query = {"query": {"match_all": {}}, "size": 1000}
                response = es_conn.es.search(index=index_name, body=query)
                chunks = [hit["_source"] for hit in response["hits"]["hits"]]
            except Exception as e:
                print(f"  ❌ Basic extraction failed: {e}")
                chunks = []

        if chunks:
            # Save extraction output
            extraction_output = {
                "index_name": index_name,
                "chunks_extracted": len(chunks),
                "extraction_timestamp": datetime.now().isoformat(),
                "chunks": chunks
            }
            save_step_output(extraction_output, "06_chunk_extraction", output_dir)

            # Also save chunks in a readable format
            readable_chunks = []
            for i, chunk in enumerate(chunks):
                readable_chunk = {
                    "chunk_id": i + 1,
                    "content": chunk.get("content_with_weight", ""),
                    "document_id": chunk.get("document_id", ""),
                    "important_keywords": chunk.get("important_keywords", [])
                }
                readable_chunks.append(readable_chunk)

            save_step_output(readable_chunks, "06_readable_chunks", output_dir)
            print(f"  ✅ Extracted and saved {len(chunks)} chunks to {output_dir}")
            return chunks
        else:
            print("  ❌ No chunks found in Elasticsearch")
            return []

    except Exception as e:
        print(f"  ❌ Failed to extract chunks: {e}")
        return []

def process_pdf_folder(folder_path, base_output_path="./direct_processing_results"):
    """Process all PDF files in a folder"""
    print(f"6. Processing PDF folder: {folder_path}")

    # Find PDF files
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found in {folder_path}")
        return

    print(f"📄 Found {len(pdf_files)} PDF files")

    # Setup components
    es_conn = setup_elasticsearch()
    embedding_model = setup_embedding_model()

    # Generate index name
    index_name = f"ragflow_direct_{get_uuid()[:8]}"
    all_chunks = []

    # Process each PDF
    for pdf_path in pdf_files:
        filename = os.path.basename(pdf_path)
        print(f"\n🔄 Processing: {filename}")

        # Create output directory for this PDF
        pdf_output_dir = create_output_directory(base_output_path, filename)
        print(f"📁 Output directory: {pdf_output_dir}")

        # Parse PDF
        sections, tables, pdf_parser = parse_pdf_document(pdf_path, pdf_output_dir)
        if not sections:
            continue

        # Chunk content
        chunks, doc_base = chunk_content(sections, tables, filename, output_dir=pdf_output_dir)
        if not chunks:
            continue

        # Tokenize chunks
        tokenized_chunks = tokenize_and_prepare_chunks(chunks, doc_base, pdf_parser, pdf_output_dir)
        if not tokenized_chunks:
            continue

        # Generate embeddings
        embedded_chunks = generate_embeddings(tokenized_chunks, embedding_model, pdf_output_dir)

        # Store in Elasticsearch
        if store_in_elasticsearch(embedded_chunks, es_conn, index_name, pdf_output_dir):
            all_chunks.extend(embedded_chunks)

    # Extract final results
    if all_chunks:
        # Create a summary output directory
        summary_output_dir = create_output_directory(base_output_path, "summary")
        extracted_chunks = extract_chunks_from_es(es_conn, index_name, summary_output_dir)

        print(f"\n🎉 Processing completed!")
        print(f"-- Total chunks processed: {len(all_chunks)}")
        print(f"-- Results saved to: {base_output_path}")
        print(f"-- Summary saved to: {summary_output_dir}")
        print(f"-- Elasticsearch index: {index_name}")

        return extracted_chunks
    else:
        print("❌ No chunks were successfully processed")
        return []

def process_single_pdf(pdf_path, output_path):
    """Process a single PDF file with specified output path"""
    print(f"- Processing single PDF: {pdf_path}")
    print(f"- Output path: {output_path}")

    if not os.path.exists(pdf_path):
        print(f"❌ PDF file does not exist: {pdf_path}")
        return []

    # Create base output directory
    os.makedirs(output_path, exist_ok=True)

    # Setup components
    es_conn = setup_elasticsearch()
    embedding_model = setup_embedding_model()

    # Generate index name
    index_name = f"ragflow_direct_{get_uuid()[:8]}"

    filename = os.path.basename(pdf_path)

    # Create output directory for this PDF
    pdf_output_dir = create_output_directory(output_path, filename)
    print(f"📁 Step outputs will be saved to: {pdf_output_dir}")

    # Parse PDF
    sections, tables, pdf_parser = parse_pdf_document(pdf_path, pdf_output_dir)
    if not sections:
        return []

    # Chunk content
    chunks, doc_base = chunk_content(sections, tables, filename, output_dir=pdf_output_dir)
    if not chunks:
        return []

    # Tokenize chunks
    tokenized_chunks = tokenize_and_prepare_chunks(chunks, doc_base, pdf_parser, pdf_output_dir)
    if not tokenized_chunks:
        return []

    # Generate embeddings
    embedded_chunks = generate_embeddings(tokenized_chunks, embedding_model, pdf_output_dir)

    # Store in Elasticsearch
    if not store_in_elasticsearch(embedded_chunks, es_conn, index_name, pdf_output_dir):
        return []

    # Extract final results
    extracted_chunks = extract_chunks_from_es(es_conn, index_name, pdf_output_dir)

    print(f"\n🎉 Single PDF processing completed!")
    print(f"📊 Total chunks processed: {len(embedded_chunks)}")
    print(f"📁 All outputs saved to: {pdf_output_dir}")
    print(f"🔍 Elasticsearch index: {index_name}")

    return extracted_chunks

def main():
    """Main function"""
    if len(sys.argv) == 1:
        # Default processing for your specific file
        pdf_path = r"/ml/shared/AI_Lab/g2module_docs/g2mod.pdf"
        output_path = r"/ml/shared/lazhang/data"

        print("🚀 Starting Direct RAGFlow Processing Pipeline")
        print("=" * 60)
        print(f"📄 Processing: {pdf_path}")
        print(f"📁 Output: {output_path}")

        chunks = process_single_pdf(pdf_path, output_path)

    elif len(sys.argv) == 2:
        # Process folder
        folder_path = sys.argv[1]

        if not os.path.exists(folder_path):
            print(f"❌ Folder does not exist: {folder_path}")
            sys.exit(1)

        print("🚀 Starting Direct RAGFlow Processing Pipeline")
        print("=" * 60)

        chunks = process_pdf_folder(folder_path)

    elif len(sys.argv) == 3:
        # Process single PDF with custom output
        pdf_path = sys.argv[1]
        output_path = sys.argv[2]

        print("🚀 Starting Direct RAGFlow Processing Pipeline")
        print("=" * 60)

        chunks = process_single_pdf(pdf_path, output_path)

    else:
        print("Usage:")
        print("  python main_direct.py                              # Process default PDF")
        print("  python main_direct.py <pdf_folder_path>            # Process folder")
        print("  python main_direct.py <pdf_path> <output_path>     # Process single PDF")
        print()
        print("Examples:")
        print("  python main_direct.py")
        print("  python main_direct.py ./documents/pdfs/")
        print("  python main_direct.py ./doc.pdf ./output/")
        sys.exit(1)

    if chunks:
        print("\n📋 Sample chunk structure:")
        sample_chunk = chunks[0]
        for key, value in sample_chunk.items():
            if isinstance(value, str) and len(value) > 100:
                print(f"  {key}: {value[:100]}...")
            elif isinstance(value, list) and len(value) > 5:
                print(f"  {key}: [{len(value)} items]")
            else:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
