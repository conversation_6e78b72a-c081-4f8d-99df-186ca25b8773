#!/usr/bin/env python3
"""
DiT Model Optimization Configuration for RAGFlow
Provides optimized settings for better DiT performance
"""

import os
import logging

class DiTOptimizationConfig:
    """
    Configuration class for optimizing DiT model performance in RAGFlow
    """
    
    # API Configuration
    API_URL = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
    API_TIMEOUT = 60  # Increased from 30 seconds
    API_RETRY_COUNT = 3
    API_RETRY_DELAY = 2  # seconds
    
    # Image Processing Optimization
    MAX_IMAGE_SIZE = (2048, 2048)  # Resize large images
    JPEG_QUALITY = 85  # Balance between quality and speed
    JPEG_OPTIMIZE = True
    
    # Layout Detection Thresholds
    CONFIDENCE_THRESHOLD = 0.1  # Lower threshold for better recall
    NMS_THRESHOLD = 0.5  # Non-maximum suppression
    
    # Table Detection Optimization
    TABLE_DETECTION_ENABLED = True
    TABLE_STRUCTURE_RECOGNITION = True
    TABLE_OCR_ADJUSTMENT = True
    
    # Batch Processing
    BATCH_SIZE = 4  # Process multiple images in batches
    CONCURRENT_REQUESTS = 2  # Parallel API calls
    
    # Performance Monitoring
    ENABLE_PERFORMANCE_LOGGING = True
    LOG_API_RESPONSE_TIME = True
    LOG_LAYOUT_DETECTION_STATS = True
    
    # DiT Model Specific Settings
    DIT_LABELS = {
        "0": "CAPTION", "1": "FIGURE", "2": "FOOTER", "3": "FORMS",
        "4": "HEADER", "5": "LIST", "6": "LOGO", "7": "TABLE",
        "8": "TEXT", "9": "TITLE", "10": "TOC"
    }
    
    # RAGFlow Label Mapping (Optimized)
    LABEL_MAPPING = {
        "TEXT": "Text",           # Main text content
        "TITLE": "Title",         # Document titles  
        "FIGURE": "Figure",       # Images, charts, diagrams
        "CAPTION": "Figure caption",  # Captions for figures/tables
        "TABLE": "Table",         # Table content - CRITICAL for table transformer
        "HEADER": "Header",       # Page headers
        "FOOTER": "Footer",       # Page footers
        "TOC": "Text",           # Table of contents → treat as text
        "LIST": "Text",          # Lists → treat as text content
        "LOGO": "Figure",        # Logos → treat as figures
        "FORMS": "Text"          # Forms → treat as text content
    }
    
    # Garbage Layout Filtering (Optimized)
    GARBAGE_LAYOUTS = ["footer", "header"]  # Minimal garbage filtering for DiT
    
    @classmethod
    def get_optimized_parser_config(cls):
        """
        Get optimized parser configuration for DiT model
        """
        return {
            # Use completely default configuration - this is KEY!
            "chunk_method": "naive",
            # DO NOT add parser_config - let RAGFlow use defaults
            # This prevents the filename_embd_weight error
        }
    
    @classmethod
    def get_dit_api_config(cls):
        """
        Get DiT API configuration
        """
        return {
            "api_url": cls.API_URL,
            "timeout": cls.API_TIMEOUT,
            "retry_count": cls.API_RETRY_COUNT,
            "retry_delay": cls.API_RETRY_DELAY,
            "max_image_size": cls.MAX_IMAGE_SIZE,
            "jpeg_quality": cls.JPEG_QUALITY,
            "jpeg_optimize": cls.JPEG_OPTIMIZE
        }
    
    @classmethod
    def setup_logging(cls):
        """
        Setup optimized logging for DiT model
        """
        if cls.ENABLE_PERFORMANCE_LOGGING:
            logging.basicConfig(
                level=logging.DEBUG,
                format='%(asctime)s - DiT - %(levelname)s - %(message)s'
            )
            
    @classmethod
    def get_table_transformer_config(cls):
        """
        Get optimized table transformer configuration for DiT
        """
        return {
            "table_detection_enabled": cls.TABLE_DETECTION_ENABLED,
            "table_structure_recognition": cls.TABLE_STRUCTURE_RECOGNITION,
            "table_ocr_adjustment": cls.TABLE_OCR_ADJUSTMENT,
            "confidence_threshold": cls.CONFIDENCE_THRESHOLD
        }

# Performance optimization recommendations
PERFORMANCE_TIPS = """
DiT Model Performance Optimization Tips:

1. API Optimization:
   - Increased timeout to 60 seconds for complex layouts
   - Image resizing to reduce API payload
   - JPEG quality optimization (85% for speed/quality balance)

2. Table Transformer Integration:
   - DiT detects TABLE regions first
   - Table Structure Recognizer processes detected regions
   - OCR adjustment for better table text recognition

3. Configuration:
   - Use completely default RAGFlow configuration
   - Avoid custom parser_config to prevent embedding errors
   - Lower confidence threshold (0.1) for better layout recall

4. Batch Processing:
   - Process multiple documents simultaneously
   - Use concurrent API calls (limit: 2 to avoid overload)
   - Monitor API response times

5. Layout Detection:
   - DiT provides 11 layout types vs 10 in standard models
   - Better figure/table/caption detection
   - Improved document structure understanding
"""

if __name__ == "__main__":
    print("DiT Model Optimization Configuration")
    print("=" * 50)
    print(f"API URL: {DiTOptimizationConfig.API_URL}")
    print(f"Timeout: {DiTOptimizationConfig.API_TIMEOUT}s")
    print(f"Max Image Size: {DiTOptimizationConfig.MAX_IMAGE_SIZE}")
    print(f"JPEG Quality: {DiTOptimizationConfig.JPEG_QUALITY}%")
    print("\nPerformance Tips:")
    print(PERFORMANCE_TIPS)
