#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
 
import os
import re
from collections import Counter
from copy import deepcopy
 
import cv2
import numpy as np
from huggingface_hub import snapshot_download
 
from api.utils.file_utils import get_project_base_directory
from deepdoc.vision import Recognizer
from deepdoc.vision.operators import nms
 
 
class LayoutRecognizer(Recognizer):
    labels = [
        "_background_",
        "Text",
        "Title",
        "Figure",
        "Figure caption",
        "Table",
        "Table caption",
        "Header",
        "Footer",
        "Reference",
        "Equation",
    ]
 
    def __init__(self, domain):
        try:
            model_dir = os.path.join(
                get_project_base_directory(),
                "rag/res/deepdoc")
            super().__init__(self.labels, domain, model_dir)
        except Exception:
            model_dir = snapshot_download(repo_id="InfiniFlow/deepdoc",
                                          local_dir=os.path.join(get_project_base_directory(), "rag/res/deepdoc"),
                                          local_dir_use_symlinks=False)
            super().__init__(self.labels, domain, model_dir)
 
        self.garbage_layouts = ["footer", "header", "reference"]
        self.client = None
        if os.environ.get("TENSORRT_DLA_SVR"):
            from deepdoc.vision.dla_cli import DLAClient
            self.client = DLAClient(os.environ["TENSORRT_DLA_SVR"])
 
    def __call__(self, image_list, ocr_res, scale_factor=3, thr=0.2, batch_size=16, drop=True):
        def __is_garbage(b):
            patt = [r"^•+$", "^[0-9]{1,2} / ?[0-9]{1,2}$",
                    r"^[0-9]{1,2} of [0-9]{1,2}$", "^http://[^ ]{12,}",
                    "\\(cid *: *[0-9]+ *\\)"
                    ]
            return any([re.search(p, b["text"]) for p in patt])
 
        if self.client:
            layouts = self.client.predict(image_list)
        else:
            layouts = super().__call__(image_list, thr, batch_size)
        # save_results(image_list, layouts, self.labels, output_dir='output/', threshold=0.7)
        assert len(image_list) == len(ocr_res)
        # Tag layout type
        boxes = []
        assert len(image_list) == len(layouts)
        garbages = {}
        page_layout = []
        for pn, lts in enumerate(layouts):
            bxs = ocr_res[pn]
            lts = [{"type": b["type"],
                    "score": float(b["score"]),
                    "x0": b["bbox"][0] / scale_factor, "x1": b["bbox"][2] / scale_factor,
                    "top": b["bbox"][1] / scale_factor, "bottom": b["bbox"][-1] / scale_factor,
                    "page_number": pn,
                    } for b in lts if float(b["score"]) >= 0.4 or b["type"] not in self.garbage_layouts]
            lts = self.sort_Y_firstly(lts, np.mean(
                [lt["bottom"] - lt["top"] for lt in lts]) / 2)
            lts = self.layouts_cleanup(bxs, lts)
            page_layout.append(lts)
 
            # Tag layout type, layouts are ready
            def findLayout(ty):
                nonlocal bxs, lts, self
                lts_ = [lt for lt in lts if lt["type"] == ty]
                i = 0
                while i < len(bxs):
                    if bxs[i].get("layout_type"):
                        i += 1
                        continue
                    if __is_garbage(bxs[i]):
                        bxs.pop(i)
                        continue
 
                    ii = self.find_overlapped_with_threashold(bxs[i], lts_,
                                                              thr=0.4)
                    if ii is None:  # belong to nothing
                        bxs[i]["layout_type"] = ""
                        i += 1
                        continue
                    lts_[ii]["visited"] = True
                    keep_feats = [
                        lts_[
                            ii]["type"] == "footer" and bxs[i]["bottom"] < image_list[pn].size[1] * 0.9 / scale_factor,
                        lts_[
                            ii]["type"] == "header" and bxs[i]["top"] > image_list[pn].size[1] * 0.1 / scale_factor,
                    ]
                    if drop and lts_[
                            ii]["type"] in self.garbage_layouts and not any(keep_feats):
                        if lts_[ii]["type"] not in garbages:
                            garbages[lts_[ii]["type"]] = []
                        garbages[lts_[ii]["type"]].append(bxs[i]["text"])
                        bxs.pop(i)
                        continue
 
                    bxs[i]["layoutno"] = f"{ty}-{ii}"
                    bxs[i]["layout_type"] = lts_[ii]["type"] if lts_[
                        ii]["type"] != "equation" else "figure"
                    i += 1
 
            for lt in ["footer", "header", "reference", "figure caption",
                       "table caption", "title", "table", "text", "figure", "equation"]:
                findLayout(lt)
 
            # add box to figure layouts which has not text box
            for i, lt in enumerate(
                    [lt for lt in lts if lt["type"] in ["figure", "equation"]]):
                if lt.get("visited"):
                    continue
                lt = deepcopy(lt)
                del lt["type"]
                lt["text"] = ""
                lt["layout_type"] = "figure"
                lt["layoutno"] = f"figure-{i}"
                bxs.append(lt)
 
            boxes.extend(bxs)
 
        ocr_res = boxes
 
        garbag_set = set()
        for k in garbages.keys():
            garbages[k] = Counter(garbages[k])
            for g, c in garbages[k].items():
                if c > 1:
                    garbag_set.add(g)
 
        ocr_res = [b for b in ocr_res if b["text"].strip() not in garbag_set]
        return ocr_res, page_layout
 
    def forward(self, image_list, thr=0.7, batch_size=16):
        return super().__call__(image_list, thr, batch_size)
 
 
class LayoutRecognizer4YOLOv10(LayoutRecognizer):
    labels = [
        "title",
        "Text",
        "Reference",
        "Figure",
        "Figure caption",
        "Table",
        "Table caption",
        "Table caption",
        "Equation",
        "Figure caption",
    ]
 
    def __init__(self, domain):
        domain = "layout"
        super().__init__(domain)
        self.auto = False
        self.scaleFill = False
        self.scaleup = True
        self.stride = 32
        self.center = True
 
    def preprocess(self, image_list):
        inputs = []
        new_shape = self.input_shape  # height, width
        for img in image_list:
            shape = img.shape[:2]  # current shape [height, width]
            # Scale ratio (new / old)
            r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
            # Compute padding
            new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
            dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
            dw /= 2  # divide padding into 2 sides
            dh /= 2
            ww, hh = new_unpad
            img = np.array(cv2.cvtColor(img, cv2.COLOR_BGR2RGB)).astype(np.float32)
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
            top, bottom = int(round(dh - 0.1)) if self.center else 0, int(round(dh + 0.1))
            left, right = int(round(dw - 0.1)) if self.center else 0, int(round(dw + 0.1))
            img = cv2.copyMakeBorder(
                img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114)
            )  # add border
            img /= 255.0
            img = img.transpose(2, 0, 1)
            img = img[np.newaxis, :, :, :].astype(np.float32)
            inputs.append({self.input_names[0]: img, "scale_factor": [shape[1]/ww, shape[0]/hh, dw, dh]})
 
        return inputs
 
    def postprocess(self, boxes, inputs, thr):
        thr = 0.08
        boxes = np.squeeze(boxes)
        scores = boxes[:, 4]
        boxes = boxes[scores > thr, :]
        scores = scores[scores > thr]
        if len(boxes) == 0:
            return []
        class_ids = boxes[:, -1].astype(int)
        boxes = boxes[:, :4]
        boxes[:, 0] -= inputs["scale_factor"][2]
        boxes[:, 2] -= inputs["scale_factor"][2]
        boxes[:, 1] -= inputs["scale_factor"][3]
        boxes[:, 3] -= inputs["scale_factor"][3]
        input_shape = np.array([inputs["scale_factor"][0], inputs["scale_factor"][1], inputs["scale_factor"][0],
                                inputs["scale_factor"][1]])
        boxes = np.multiply(boxes, input_shape, dtype=np.float32)
 
        unique_class_ids = np.unique(class_ids)
        indices = []
        for class_id in unique_class_ids:
            class_indices = np.where(class_ids == class_id)[0]
            class_boxes = boxes[class_indices, :]
            class_scores = scores[class_indices]
            class_keep_boxes = nms(class_boxes, class_scores, 0.45)
            indices.extend(class_indices[class_keep_boxes])
 
        return [{
            "type": self.label_list[class_ids[i]].lower(),
            "bbox": [float(t) for t in boxes[i].tolist()],
            "score": float(scores[i])
        } for i in indices]
 
 
class CustomDiTLayoutRecognizer:
    """
    API-based DiT layout recognizer that follows the same pattern as original LayoutRecognizer
    """
    # DiT model's actual labels from index_to_name.json
    dit_labels = {
        "0": "CAPTION", "1": "FIGURE", "2": "FOOTER", "3": "FORMS",
        "4": "HEADER", "5": "LIST", "6": "LOGO", "7": "TABLE",
        "8": "TEXT", "9": "TITLE", "10": "TOC"
    }
  
 
    # RAGFlow compatible labels (for compatibility)
    labels = [
        "_background_",
        "Text",
        "Title",
        "Figure",
        "Figure caption",  # Maps to CAPTION
        "Table",
        "Table caption",   # Maps to CAPTION when near table
        "Header",
        "Footer",
        "Reference",       # Maps to TOC
        "Equation",        # Maps to FIGURE
        "List",           # New: Maps to LIST
        "Logo",           # New: Maps to LOGO
        "Forms"           # New: Maps to FORMS
    ]
 
    def __init__(self, domain="layout"):
        """
        Initialize the DiT layout recognizer using API calls with optimized configuration

        Args:
            domain: Domain parameter (for compatibility with RAGFlow interface)
        """
        # API endpoint for DiT layout detection
        self.api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
        self.garbage_layouts = ["footer", "header"]

        # # Performance optimization settings
        # self.api_timeout = 60  # Increased timeout for complex layouts
        # self.api_retry_count = 3
        # self.api_retry_delay = 2
        # self.max_image_size = (2048, 2048)
        # self.jpeg_quality = 85
        # self.confidence_threshold = 0.1  # Lower threshold for better recall

        # # Table transformer integration flag
        # self.table_detection_enabled = True

        # # Performance monitoring
        # self.enable_performance_logging = True
 
 
 
    def map_dit_to_ragflow_label(self, dit_label, bbox=None):
        """
        Map DiT model labels to RAGFlow compatible labels
 
        Args:
            dit_label: Original DiT label (e.g., "CAPTION", "FIGURE")
            bbox: Bounding box coordinates (for context-aware mapping)
 
        Returns:
            RAGFlow compatible label
        """
        mapping = {
            "TEXT": "text",           # Main text content 
            "TITLE": "title",         # Document titles 
            "FIGURE": "figure",       # Images, charts, diagrams 
            "CAPTION": "figure caption",  # Captions for figures/tables 
            "TABLE": "table",         # Table content 
            "HEADER": "header",       # Page headers 
            "FOOTER": "footer",       # Page footers 
            "TOC": "text",            # Table of contents → Reference 
            "LIST": "text",           # Lists → treat as text content 
            "LOGO": "figure",         # Logos → treat as figures 
            "FORMS": "text"           # Forms → treat as text content 
        }
        return mapping.get(dit_label.upper(), "text") 
 
    def call_api(self, image):
        """
        Call the DiT API with a single image 
        """
        import requests
        import base64
        import io
        import logging

        # Optimize image encoding using instance settings
        buf = io.BytesIO()
        # Resize large images to reduce API payload
        # if image.size[0] > self.max_image_size[0] or image.size[1] > self.max_image_size[1]:
        #     image.thumbnail(self.max_image_size)

        #image.save(buf, format="JPEG", quality=self.jpeg_quality, optimize=True)
        image.save(buf, format="JPEG")
        img_bytes = buf.getvalue()
        encoded_image = base64.b64encode(img_bytes).decode("utf-8")

        # Prepare API request
        post_data = {"inputs": [encoded_image]}

        try:
            response = requests.post(
                self.api_url,
                json=post_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            result = response.json()

            # Log successful API call
            logging.debug(f"DiT API call successful for image size: {image.size}")
            return result.get("outputs", {})

        except requests.exceptions.Timeout:
            logging.warning(f"DiT API timeout for image size: {image.size}")
            return {}
        except requests.exceptions.RequestException as e:
            logging.error(f"DiT API request failed: {e}")
            return {}
        except Exception as e:
            logging.error(f"DiT API unexpected error: {e}")
            return {}
 
    def __call__(self, image_list, ocr_res, scale_factor=3, thr=0.2, batch_size=16, drop=True):
        """
        Main layout detection method - follows the same pattern as original LayoutRecognizer
        """
        def __is_garbage(b):
            patt = [r"^•+$", "^[0-9]{1,2} / ?[0-9]{1,2}$",
                    r"^[0-9]{1,2} of [0-9]{1,2}$", "^http://[^ ]{12,}",
                    "\\(cid *: *[0-9]+ *\\)"
                    ]
            return any([re.search(p, b["text"]) for p in patt])
 
        # Call API for each image to get layout detection results
        layouts = []
        for page_idx, img in enumerate(image_list):
            api_result = self.call_api(img)
 
            # Convert API result to expected format
            layout_boxes = []
            if api_result.get('boxes') and api_result.get('classes') and api_result.get('scores'):
                for box, class_name, score in zip(api_result['boxes'], api_result['classes'], api_result['scores']):
                    if score >= 0.1: 
                        # Map DiT labels to RAGFlow compatible labels
                        ragflow_label = self.map_dit_to_ragflow_label(class_name, box)
                        layout_boxes.append({
                            "type": ragflow_label,
                            "score": float(score),
                            "bbox": [float(x) for x in box],  # [x1, y1, x2, y2]
                            "original_dit_label": class_name  # Keep original for debugging
                        })
            layouts.append(layout_boxes)
 
        # Process layouts same as original LayoutRecognizer
        assert len(image_list) == len(ocr_res)
        boxes = []
        assert len(image_list) == len(layouts)
        garbages = {}
        page_layout = []
 
        for pn, lts in enumerate(layouts):
            bxs = ocr_res[pn]
            # Use same threshold logic as YOLO model
            lts = [{"type": b["type"],
                    "score": float(b["score"]),
                    "x0": b["bbox"][0] / scale_factor, "x1": b["bbox"][2] / scale_factor,
                    "top": b["bbox"][1] / scale_factor, "bottom": b["bbox"][-1] / scale_factor,
                    "page_number": pn,
                    } for b in lts if float(b["score"]) >= 0.2 or b["type"] not in self.garbage_layouts]
            lts = self.sort_Y_firstly(lts, np.mean(
                [lt["bottom"] - lt["top"] for lt in lts]) / 2 if lts else 0)
            lts = self.layouts_cleanup(bxs, lts)
            page_layout.append(lts)
 
            # Tag layout type, layouts are ready
            def findLayout(ty):
                nonlocal bxs, lts, self
                lts_ = [lt for lt in lts if lt["type"] == ty]
                i = 0
                while i < len(bxs):
                    if bxs[i].get("layout_type"):
                        i += 1
                        continue
                    if __is_garbage(bxs[i]):
                        bxs.pop(i)
                        continue
 
                    ii = self.find_overlapped_with_threashold(bxs[i], lts_,
                                                              thr=0.4)
                    if ii is None:  # belong to nothing
                        bxs[i]["layout_type"] = ""
                        i += 1
                        continue
                    lts_[ii]["visited"] = True
                    keep_feats = [
                        lts_[
                            ii]["type"] == "footer" and bxs[i]["bottom"] < image_list[pn].size[1] * 0.9 / scale_factor,
                        lts_[
                            ii]["type"] == "header" and bxs[i]["top"] > image_list[pn].size[1] * 0.1 / scale_factor,
                    ]
                    if drop and lts_[
                            ii]["type"] in self.garbage_layouts and not any(keep_feats):
                        if lts_[ii]["type"] not in garbages:
                            garbages[lts_[ii]["type"]] = []
                        garbages[lts_[ii]["type"]].append(bxs[i]["text"])
                        bxs.pop(i)
                        continue
 
                    bxs[i]["layoutno"] = f"{ty}-{ii}"
                    bxs[i]["layout_type"] = lts_[ii]["type"] if lts_[
                        ii]["type"] != "equation" else "figure"
                    i += 1
 
            for lt in ["footer", "header", "reference", "figure caption",
                       "table caption", "title", "table", "text", "figure", "equation"]:
                findLayout(lt)
 
            # add box to figure layouts which has not text box
            for i, lt in enumerate(
                    [lt for lt in lts if lt["type"] in ["figure", "equation"]]):
                if lt.get("visited"):
                    continue
                lt = deepcopy(lt)
                del lt["type"]
                lt["text"] = ""
                lt["layout_type"] = "figure"
                lt["layoutno"] = f"figure-{i}"
                bxs.append(lt)
 
            boxes.extend(bxs)
 
        ocr_res = boxes
 
        garbag_set = set()
        for k in garbages.keys():
            garbages[k] = Counter(garbages[k])
            for g, c in garbages[k].items():
                if c > 1:
                    garbag_set.add(g)
 
        ocr_res = [b for b in ocr_res if b["text"].strip() not in garbag_set]
        return ocr_res, page_layout

    # def get_table_regions(self, page_layout):
    #     """
    #     Extract table regions detected by DiT for table transformer processing

    #     This method is called by PDF parser's _table_transformer_job method
    #     to get table regions that need detailed structure analysis.

    #     Args:
    #         page_layout: Layout detection results from DiT

    #     Returns:
    #         List of table regions with coordinates for cropping
    #     """
    #     table_regions = []
    #     for page_idx, layouts in enumerate(page_layout):
    #         page_tables = [layout for layout in layouts if layout["type"] == "table"]
    #         for table in page_tables:
    #             table_regions.append({
    #                 "page": page_idx,
    #                 "bbox": [table["x0"], table["top"], table["x1"], table["bottom"]],
    #                 "score": table["score"],
    #                 "original_dit_label": table.get("original_dit_label", "TABLE")
    #             })
    #     return table_regions

    # def get_optimization_stats(self):
    #     """
    #     Get current optimization settings for monitoring
    #     """
    #     return {
    #         "api_url": self.api_url,
    #         "api_timeout": self.api_timeout,
    #         "max_image_size": self.max_image_size,
    #         "jpeg_quality": self.jpeg_quality,
    #         "confidence_threshold": self.confidence_threshold,
    #         "table_detection_enabled": self.table_detection_enabled
    #     }

    # Helper methods copied from original LayoutRecognizer
    def sort_Y_firstly(self, boxes, threashold):
        """Sort boxes by Y coordinate first, then X coordinate"""
        if not boxes:
            return boxes
        boxes = sorted(boxes, key=lambda b: (b["top"], b["x0"]))
        return boxes
 
    def layouts_cleanup(self, boxes, layouts):
        """Clean up layout boxes"""
        return layouts
 
    def overlapped_area(self, a, b):
        """Calculate overlapped area between two boxes"""
        return max(0, min(a["x1"], b["x1"]) - max(a["x0"], b["x0"])) * \
               max(0, min(a["bottom"], b["bottom"]) - max(a["top"], b["top"]))
 
    def find_overlapped_with_threashold(self, box, boxes, thr=0.4):
        """Find overlapped box with threshold"""
        if not boxes:
            return None
 
        max_overlap = 0
        max_idx = None
        box_area = (box["x1"] - box["x0"]) * (box["bottom"] - box["top"])
 
        for i, b in enumerate(boxes):
            overlap = self.overlapped_area(box, b)
            if overlap > 0:
                overlap_ratio = overlap / box_area
                if overlap_ratio >= thr and overlap_ratio > max_overlap:
                    max_overlap = overlap_ratio
                    max_idx = i
 
        return max_idx
 
 
 