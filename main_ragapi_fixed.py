#!/usr/bin/env python3
"""
Fixed RAGFlow PDF Processing Script
Addresses the embedding error by extracting chunks before embedding fails
"""

import os
import sys
import time
import json
import glob
from datetime import datetime
from main_ragapi import RAGFlowAPIClient

def extract_chunks_before_embedding_fails(folder_path, output_base, api_base_url, api_key, dataset_name="testrag_fixed"):
    """
    Fixed version: Extract chunks immediately after they're generated, before embedding fails
    """
    print(f"🚀 Fixed RAGFlow Processing - Avoiding Embedding Errors")
    print(f"📁 Processing PDFs from: {folder_path}")
    
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found")
        return []

    print(f"📄 Found {len(pdf_files)} PDF files")
    print(f"📦 Using dataset: {dataset_name}")

    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_base, f"{dataset_name}_fixed_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Output directory: {output_dir}")

    # Step 1: Create or find dataset
    print(f"\n1. 📦 Creating/finding dataset: {dataset_name}")
    dataset_id = client.create_or_find_dataset(dataset_name)
    if not dataset_id:
        print("❌ Failed to create or find dataset")
        return []

    print(f"   ✅ Dataset ready: {dataset_id}")

    # Step 2: Process files with improved duplicate checking
    print(f"\n2. 🔍 Processing files with duplicate checking...")
    document_ids = []
    new_uploads = 0
    existing_docs = 0
    
    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")

        try:
            # Check if document already exists
            doc_id = client.check_document_exists(dataset_id, filename)
            
            if doc_id:
                print(f"   ♻️ Document already exists: {doc_id}")
                print(f"   ⏭️ Skipping upload, will include in processing")
                document_ids.append(doc_id)
                existing_docs += 1
            else:
                print(f"   📤 Uploading new document...")
                doc_id = client.upload_document(dataset_id, pdf_path)
                if not doc_id:
                    print(f"   ❌ Upload failed for {filename}")
                    continue
                print(f"   ✅ Uploaded successfully: {doc_id}")
                document_ids.append(doc_id)
                new_uploads += 1

        except Exception as e:
            print(f"   ❌ Error processing {filename}: {str(e)}")

    if not document_ids:
        print("❌ No documents to process")
        return []

    print(f"\n📊 Processing Summary:")
    print(f"   📄 Total documents: {len(document_ids)}")
    print(f"   📤 New uploads: {new_uploads}")
    print(f"   ♻️ Existing documents: {existing_docs}")

    # Step 3: Process each document individually to catch chunks before embedding fails
    print(f"\n3. 🔄 Processing documents individually to avoid embedding errors...")
    
    all_extracted_chunks = []
    
    for i, doc_id in enumerate(document_ids, 1):
        print(f"\n📄 Processing document {i}/{len(document_ids)}: {doc_id}")
        
        # Initiate parsing for this document
        print(f"   🚀 Initiating parsing...")
        parse_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        parse_payload = {"document_ids": [doc_id]}
        
        try:
            parse_response = client.session.post(parse_url, json=parse_payload)
            if parse_response.status_code == 200:
                parse_result = parse_response.json()
                if parse_result.get("code") == 0:
                    print(f"   ✅ Parsing initiated")
                else:
                    print(f"   ❌ Parsing failed: {parse_result.get('message')}")
                    continue
            else:
                print(f"   ❌ Parsing HTTP error: {parse_response.status_code}")
                continue
        except Exception as e:
            print(f"   ❌ Parsing request failed: {str(e)}")
            continue
        
        # Monitor parsing progress and extract chunks as soon as they're generated
        print(f"   ⏳ Monitoring parsing progress...")
        
        chunks_extracted = False
        for check_count in range(30):  # Check for 5 minutes (30 * 10 seconds)
            time.sleep(10)
            
            try:
                # Check document status
                status_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/documents"
                status_response = client.session.get(status_url)
                
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    if status_result.get("code") == 0:
                        docs_data = status_result.get("data", {})
                        docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data
                        
                        # Find our document
                        current_doc = None
                        for doc in docs_list:
                            if doc.get("id") == doc_id:
                                current_doc = doc
                                break
                        
                        if current_doc:
                            status = current_doc.get('run', 'unknown')
                            progress = current_doc.get('progress', 0)
                            chunks = current_doc.get('chunk_count', 0)
                            progress_msg = current_doc.get('progress_msg', '')
                            
                            print(f"   📊 Status: {status}, Progress: {progress:.1%}, Chunks: {chunks}")
                            
                            # If chunks are generated, try to extract them immediately
                            if chunks > 0 and not chunks_extracted:
                                print(f"   🎯 Chunks detected! Attempting immediate extraction...")
                                
                                # Try multiple extraction methods
                                extracted_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                                
                                if extracted_chunks:
                                    print(f"   ✅ Successfully extracted {len(extracted_chunks)} chunks!")
                                    all_extracted_chunks.extend(extracted_chunks)
                                    chunks_extracted = True
                                    break
                                else:
                                    print(f"   ⚠️ Chunks detected but extraction failed, continuing to monitor...")
                            
                            # Check if processing completed successfully
                            if status == 'FINISH':
                                print(f"   🎉 Processing completed successfully!")
                                if not chunks_extracted:
                                    extracted_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                                    if extracted_chunks:
                                        all_extracted_chunks.extend(extracted_chunks)
                                        chunks_extracted = True
                                break
                            
                            # Check if processing failed
                            elif status == 'FAIL':
                                print(f"   ❌ Processing failed: {progress_msg}")
                                
                                # Even if it failed, try to extract any chunks that were generated
                                if chunks > 0 and not chunks_extracted:
                                    print(f"   🔄 Attempting to salvage {chunks} chunks before failure...")
                                    extracted_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                                    if extracted_chunks:
                                        print(f"   ✅ Salvaged {len(extracted_chunks)} chunks!")
                                        all_extracted_chunks.extend(extracted_chunks)
                                        chunks_extracted = True
                                break
                            
                            # Check for embedding error in progress message
                            if 'embedding error' in progress_msg.lower() or 'float()' in progress_msg:
                                print(f"   ⚠️ Embedding error detected! Attempting chunk extraction...")
                                if chunks > 0:
                                    extracted_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                                    if extracted_chunks:
                                        print(f"   ✅ Extracted {len(extracted_chunks)} chunks before embedding failure!")
                                        all_extracted_chunks.extend(extracted_chunks)
                                        chunks_extracted = True
                                break
                
            except Exception as e:
                print(f"   ⚠️ Status check failed: {str(e)}")
            
            print(f"   ⏳ Still processing... ({(check_count+1)*10}s elapsed)")
        
        if not chunks_extracted:
            print(f"   ❌ No chunks extracted for this document")

    # Step 4: Save results
    print(f"\n4. 💾 Saving extracted results...")
    
    if all_extracted_chunks:
        output_file = os.path.join(output_dir, f"{dataset_name}_extracted_chunks.json")
        
        output_data = {
            "dataset_name": dataset_name,
            "dataset_id": dataset_id,
            "processing_summary": {
                "total_pdf_files": len(pdf_files),
                "new_uploads": new_uploads,
                "existing_documents": existing_docs,
                "total_documents_processed": len(document_ids),
                "total_chunks_extracted": len(all_extracted_chunks),
                "extraction_method": "pre_embedding_extraction",
                "extraction_timestamp": datetime.now().isoformat()
            },
            "document_ids": document_ids,
            "chunks": all_extracted_chunks
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Results saved to: {output_file}")
        
        print(f"\n🎉 Processing Complete!")
        print(f"📊 Final Summary:")
        print(f"   📦 Dataset: {dataset_name}")
        print(f"   📄 PDF files: {len(pdf_files)}")
        print(f"   📤 New uploads: {new_uploads}")
        print(f"   ♻️ Existing documents: {existing_docs}")
        print(f"   🧩 Total chunks extracted: {len(all_extracted_chunks)}")
        print(f"   💾 Output file: {output_file}")
        
        return all_extracted_chunks
    else:
        print("❌ No chunks were extracted from any documents")
        return []

def main():
    """Main function"""
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
    OUTPUT_BASE = r"/ml/shared/lazhang/data"

    print("🚀 RAGFlow Fixed Processing - Avoiding Embedding Errors")
    print("=" * 70)
    print("✅ Features:")
    print("   - Duplicate file checking (no re-upload)")
    print("   - Individual document processing")
    print("   - Chunk extraction before embedding fails")
    print("   - Salvage chunks from failed processing")
    print("   - Comprehensive error handling")
    print()

    chunks = extract_chunks_before_embedding_fails(
        PDF_FOLDER, 
        OUTPUT_BASE, 
        API_BASE_URL, 
        API_KEY, 
        "testrag_fixed"
    )
    
    if chunks:
        print(f"\n🎉 SUCCESS: Extracted {len(chunks)} chunks!")
        if chunks and chunks[0].get('content'):
            sample = chunks[0]['content'][:200]
            print(f"📄 Sample: '{sample}{'...' if len(chunks[0]['content']) > 200 else ''}'")
    else:
        print(f"\n❌ No chunks extracted - check logs above")

if __name__ == "__main__":
    main()
