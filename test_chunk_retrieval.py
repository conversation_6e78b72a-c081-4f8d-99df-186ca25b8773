#!/usr/bin/env python3
"""
Test chunk retrieval from the fixed dataset
"""

import os
import json
from main_ragapi import RAGFlowAPIClient

def test_chunk_retrieval():
    """Test retrieving chunks from the fixed dataset"""
    print("🧪 Testing Chunk Retrieval from Fixed Dataset")
    print("=" * 60)
    
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")
    DATASET_NAME = "testrag_embedding_fixed"
    
    try:
        client = RAGFlowAPIClient(API_BASE_URL, API_KEY)
        
        # Find the fixed dataset
        print(f"1. 🔍 Finding dataset: {DATASET_NAME}")
        
        url = f"{API_BASE_URL}/api/v1/datasets"
        response = client.session.get(url)
        
        dataset_id = None
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                datasets = result.get("data", [])
                for ds in datasets:
                    if ds.get("name") == DATASET_NAME:
                        dataset_id = ds.get("id")
                        print(f"   ✅ Found dataset: {dataset_id}")
                        break
        
        if not dataset_id:
            print(f"   ❌ Dataset not found: {DATASET_NAME}")
            return False
        
        # Check documents in the dataset
        print(f"\n2. 📄 Checking documents in dataset...")
        docs_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        docs_response = client.session.get(docs_url)
        
        if docs_response.status_code == 200:
            docs_result = docs_response.json()
            if docs_result.get("code") == 0:
                docs_data = docs_result.get("data", {})
                docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data
                
                print(f"   📊 Found {len(docs_list)} documents:")
                
                for i, doc in enumerate(docs_list, 1):
                    name = doc.get("name", "Unknown")
                    doc_id = doc.get("id", "Unknown")
                    status = doc.get("run", "Unknown")
                    chunks = doc.get("chunk_count", 0)
                    progress = doc.get("progress", 0)
                    
                    print(f"      {i}. {name}")
                    print(f"         ID: {doc_id}")
                    print(f"         Status: {status}, Progress: {progress:.1%}")
                    print(f"         Chunks: {chunks}")
                    
                    # Try to retrieve chunks for this document
                    if chunks > 0:
                        print(f"         🔍 Attempting to retrieve {chunks} chunks...")
                        
                        # Method 1: Direct document chunks API
                        chunk_url = f"{API_BASE_URL}/api/v1/datasets/{dataset_id}/documents/{doc_id}/chunks"
                        chunk_response = client.session.get(chunk_url)
                        
                        if chunk_response.status_code == 200:
                            chunk_result = chunk_response.json()
                            if chunk_result.get("code") == 0:
                                retrieved_chunks = chunk_result.get("data", [])
                                print(f"         ✅ Retrieved {len(retrieved_chunks)} chunks via direct API!")
                                
                                if retrieved_chunks:
                                    # Show sample chunk
                                    sample = retrieved_chunks[0]
                                    content = sample.get("content", "")[:100]
                                    print(f"         📄 Sample: '{content}{'...' if len(sample.get('content', '')) > 100 else ''}'")
                                    
                                    # Save chunks to file
                                    output_file = f"retrieved_chunks_{DATASET_NAME}.json"
                                    output_data = {
                                        "dataset_name": DATASET_NAME,
                                        "dataset_id": dataset_id,
                                        "document_name": name,
                                        "document_id": doc_id,
                                        "total_chunks": len(retrieved_chunks),
                                        "chunks": retrieved_chunks
                                    }
                                    
                                    with open(output_file, 'w', encoding='utf-8') as f:
                                        json.dump(output_data, f, indent=2, ensure_ascii=False)
                                    
                                    print(f"         💾 Chunks saved to: {output_file}")
                                    return True
                            else:
                                print(f"         ❌ Chunk retrieval failed: {chunk_result.get('message')}")
                        else:
                            print(f"         ❌ Chunk retrieval HTTP error: {chunk_response.status_code}")
                        
                        # Method 2: Try retrieval API
                        print(f"         🔍 Trying retrieval API...")
                        retrieved_chunks = client.retrieve_chunks(dataset_id, [doc_id])
                        
                        if retrieved_chunks:
                            print(f"         ✅ Retrieved {len(retrieved_chunks)} chunks via retrieval API!")
                            
                            # Save chunks to file
                            output_file = f"retrieved_chunks_via_api_{DATASET_NAME}.json"
                            output_data = {
                                "dataset_name": DATASET_NAME,
                                "dataset_id": dataset_id,
                                "document_name": name,
                                "document_id": doc_id,
                                "total_chunks": len(retrieved_chunks),
                                "chunks": retrieved_chunks
                            }
                            
                            with open(output_file, 'w', encoding='utf-8') as f:
                                json.dump(output_data, f, indent=2, ensure_ascii=False)
                            
                            print(f"         💾 Chunks saved to: {output_file}")
                            return True
                        else:
                            print(f"         ❌ Retrieval API also failed")
                    else:
                        print(f"         ⚠️ No chunks available yet")
        
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Chunk Retrieval Test")
    print("=" * 50)
    
    success = test_chunk_retrieval()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Chunk retrieval test PASSED!")
        print("\n✅ This confirms:")
        print("   - The embedding error fix works")
        print("   - Chunks are successfully generated")
        print("   - Chunks can be retrieved via API")
        print("   - The complete workflow is now functional")
    else:
        print("❌ Chunk retrieval test failed")
        print("\n🔍 Possible reasons:")
        print("   - Processing may still be in progress")
        print("   - Chunks may not be fully indexed yet")
        print("   - API endpoint may need different parameters")
    
    print(f"\n📝 Next steps:")
    print("   1. If successful: Use the main script with fixed configuration")
    print("   2. If failed: Wait for processing to complete and retry")
    print("   3. Check the saved JSON files for chunk content")

if __name__ == "__main__":
    main()
