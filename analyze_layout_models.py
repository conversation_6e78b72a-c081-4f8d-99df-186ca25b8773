#!/usr/bin/env python3
"""
Comprehensive analysis of YOLO vs DiT layout models
Captures Docker logs and analyzes table detection differences
"""

import os
import time
import json
import subprocess
import requests
from datetime import datetime

class LayoutModelAnalyzer:
    def __init__(self):
        self.api_base_url = "http://localhost:9380"
        self.api_key = "ragflow-YWRtaW46aW5maW5pdGVfcmFnZmxvdw=="
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
    
    def capture_logs_during_processing(self, model_name, duration=180):
        """Capture Docker logs during document processing"""
        print(f"📋 Capturing logs for {model_name} model...")
        
        log_file = f"logs_{model_name.lower()}_{int(time.time())}.txt"
        
        try:
            # Capture all relevant logs
            cmd = f"timeout {duration} docker logs -f ragflow-server 2>&1 | tee {log_file}"
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            return process, log_file
        except Exception as e:
            print(f"❌ Log capture error: {str(e)}")
            return None, None
    
    def analyze_log_file(self, log_file, model_name):
        """Analyze captured log file for layout and table processing"""
        if not os.path.exists(log_file):
            print(f"❌ Log file not found: {log_file}")
            return {}
        
        print(f"🔍 Analyzing {log_file} for {model_name} model...")
        
        with open(log_file, 'r') as f:
            logs = f.read()
        
        # Extract relevant information
        analysis = {
            "model": model_name,
            "log_file": log_file,
            "total_lines": len(logs.split('\n')),
            "layout_analysis_entries": [],
            "table_analysis_entries": [],
            "layout_times": [],
            "table_times": [],
            "dit_api_calls": [],
            "yolo_references": [],
            "table_detections": []
        }
        
        lines = logs.split('\n')
        
        for line in lines:
            # Layout analysis timing
            if "Layout analysis" in line:
                analysis["layout_analysis_entries"].append(line.strip())
                # Extract timing
                if "(" in line and "s)" in line:
                    try:
                        time_str = line.split("(")[1].split("s)")[0]
                        analysis["layout_times"].append(float(time_str))
                    except:
                        pass
            
            # Table analysis timing
            if "Table analysis" in line:
                analysis["table_analysis_entries"].append(line.strip())
                # Extract timing
                if "(" in line and "s)" in line:
                    try:
                        time_str = line.split("(")[1].split("s)")[0]
                        analysis["table_times"].append(float(time_str))
                    except:
                        pass
            
            # DiT API calls
            if "mlrun-datahub" in line or "DiT" in line:
                analysis["dit_api_calls"].append(line.strip())
            
            # YOLO references
            if "YOLO" in line or "yolo" in line:
                analysis["yolo_references"].append(line.strip())
            
            # Table detections
            if "table" in line.lower() and ("detected" in line.lower() or "found" in line.lower()):
                analysis["table_detections"].append(line.strip())
        
        # Calculate statistics
        analysis["avg_layout_time"] = sum(analysis["layout_times"]) / len(analysis["layout_times"]) if analysis["layout_times"] else 0
        analysis["avg_table_time"] = sum(analysis["table_times"]) / len(analysis["table_times"]) if analysis["table_times"] else 0
        analysis["total_layout_calls"] = len(analysis["layout_analysis_entries"])
        analysis["total_table_calls"] = len(analysis["table_analysis_entries"])
        analysis["dit_api_call_count"] = len(analysis["dit_api_calls"])
        analysis["yolo_reference_count"] = len(analysis["yolo_references"])
        
        print(f"📊 {model_name} Analysis Summary:")
        print(f"   Layout analysis calls: {analysis['total_layout_calls']}")
        print(f"   Table analysis calls: {analysis['total_table_calls']}")
        print(f"   Average layout time: {analysis['avg_layout_time']:.3f}s")
        print(f"   Average table time: {analysis['avg_table_time']:.3f}s")
        print(f"   DiT API calls: {analysis['dit_api_call_count']}")
        print(f"   YOLO references: {analysis['yolo_reference_count']}")
        
        return analysis
    
    def test_with_layout_model(self, layout_model, test_pdf_path):
        """Test document processing with specific layout model"""
        print(f"\n{'='*20} Testing {layout_model} Model {'='*20}")
        
        # Create unique dataset for this test
        timestamp = int(time.time())
        dataset_name = f"layout_test_{layout_model.lower()}_{timestamp}"
        
        # Create dataset
        dataset_id = self.create_dataset(dataset_name)
        if not dataset_id:
            return None
        
        # Upload document
        doc_id = self.upload_document(dataset_id, test_pdf_path)
        if not doc_id:
            return None
        
        # Start log capture
        log_process, log_file = self.capture_logs_during_processing(layout_model, duration=300)
        
        # Parse with specific layout model
        success = self.parse_with_layout_model(dataset_id, doc_id, layout_model)
        if not success:
            if log_process:
                log_process.terminate()
            return None
        
        # Wait for completion
        print(f"⏳ Waiting for {layout_model} processing to complete...")
        completed = self.wait_for_completion(dataset_id, doc_id, timeout=240)
        
        # Stop log capture
        if log_process:
            time.sleep(5)  # Allow final logs to be captured
            log_process.terminate()
        
        # Analyze logs
        if log_file:
            analysis = self.analyze_log_file(log_file, layout_model)
            analysis["processing_completed"] = completed
            analysis["dataset_id"] = dataset_id
            analysis["doc_id"] = doc_id
            return analysis
        
        return None
    
    def create_dataset(self, name):
        """Create a dataset for testing"""
        url = f"{self.api_base_url}/api/v1/datasets"
        payload = {"name": name, "description": f"Layout model test: {name}"}
        
        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    dataset_id = result.get("data", {}).get("id")
                    print(f"✅ Created dataset: {name} (ID: {dataset_id})")
                    return dataset_id
            print(f"❌ Dataset creation failed: {response.text}")
        except Exception as e:
            print(f"❌ Dataset creation error: {str(e)}")
        return None
    
    def upload_document(self, dataset_id, file_path):
        """Upload document to dataset"""
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'application/pdf')}
                response = self.session.post(url, files=files)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    doc_id = result.get("data", {}).get("id")
                    print(f"✅ Uploaded: {os.path.basename(file_path)} (ID: {doc_id})")
                    return doc_id
            print(f"❌ Upload failed: {response.text}")
        except Exception as e:
            print(f"❌ Upload error: {str(e)}")
        return None
    
    def parse_with_layout_model(self, dataset_id, doc_id, layout_model):
        """Parse document with specific layout model"""
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        # Map layout model names to RAGFlow configuration
        layout_config = {
            "DiT": "DeepDOC",  # DiT is used when layout_recognize = "DeepDOC"
            "YOLO": "YOLO",    # Direct YOLO reference
            "DeepDOC": "DeepDOC"  # Default DiT
        }
        
        parser_config = {
            "layout_recognize": layout_config.get(layout_model, "DeepDOC"),
            "chunk_method": "paper"
        }
        
        payload = {
            "document_ids": [doc_id],
            "parser_config": parser_config
        }
        
        print(f"🔄 Parsing with {layout_model} (config: {parser_config['layout_recognize']})...")
        
        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print(f"✅ Parsing initiated with {layout_model}")
                    return True
            print(f"❌ Parsing failed: {response.text}")
        except Exception as e:
            print(f"❌ Parsing error: {str(e)}")
        return False
    
    def wait_for_completion(self, dataset_id, doc_id, timeout=300):
        """Wait for document processing to complete"""
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{doc_id}"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        doc_data = result.get("data", {})
                        status = doc_data.get("status", "UNKNOWN")
                        progress = doc_data.get("progress", 0)
                        
                        if status == "DONE":
                            print(f"✅ Processing completed! Progress: {progress}%")
                            return True
                        elif status == "FAIL":
                            print(f"❌ Processing failed! Progress: {progress}%")
                            return False
                        else:
                            print(f"   Status: {status}, Progress: {progress}%")
                
                time.sleep(15)
            except Exception as e:
                print(f"   Status check error: {str(e)}")
                time.sleep(15)
        
        print(f"⏰ Timeout after {timeout}s")
        return False

def main():
    print("🧪 Comprehensive Layout Model Analysis: YOLO vs DiT")
    print("=" * 70)
    
    analyzer = LayoutModelAnalyzer()
    
    # Test file with potential tables
    test_file = "/ml/shared/lazhang/data/pdfdata/g2mod.pdf"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    print(f"📄 Test file: {os.path.basename(test_file)}")
    
    # Test both models
    models_to_test = ["DiT", "YOLO"]
    results = {}
    
    for model in models_to_test:
        print(f"\n🔬 Testing {model} Model...")
        result = analyzer.test_with_layout_model(model, test_file)
        if result:
            results[model] = result
        else:
            print(f"❌ {model} test failed")
    
    # Compare results
    print(f"\n{'='*20} DETAILED COMPARISON {'='*20}")
    
    for model, result in results.items():
        print(f"\n{model} Model Results:")
        print(f"  Processing completed: {result.get('processing_completed', False)}")
        print(f"  Layout analysis calls: {result.get('total_layout_calls', 0)}")
        print(f"  Table analysis calls: {result.get('total_table_calls', 0)}")
        print(f"  Average layout time: {result.get('avg_layout_time', 0):.3f}s")
        print(f"  Average table time: {result.get('avg_table_time', 0):.3f}s")
        print(f"  DiT API calls: {result.get('dit_api_call_count', 0)}")
        print(f"  YOLO references: {result.get('yolo_reference_count', 0)}")
        
        if result.get('table_analysis_entries'):
            print(f"  Sample table analysis: {result['table_analysis_entries'][0]}")
    
    # Save comprehensive results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"comprehensive_layout_analysis_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Comprehensive results saved to: {results_file}")
    
    # Key insights
    if len(results) >= 2:
        print(f"\n🔍 Key Insights:")
        dit_result = results.get("DiT", {})
        yolo_result = results.get("YOLO", {})
        
        dit_table_time = dit_result.get("avg_table_time", 0)
        yolo_table_time = yolo_result.get("avg_table_time", 0)
        
        print(f"   DiT table processing: {dit_table_time:.3f}s average")
        print(f"   YOLO table processing: {yolo_table_time:.3f}s average")
        
        if dit_table_time == 0 and yolo_table_time == 0:
            print("   ⚠️ Both models show 0.00s table processing - no tables detected")
        elif dit_table_time > 0 or yolo_table_time > 0:
            print("   ✅ Table processing detected in at least one model")

if __name__ == "__main__":
    main()
