#!/usr/bin/env python3
# Configuration fix to switch from DeepDOC to DiT

import requests
import json

def update_dataset_config():
    '''Update dataset configuration to use DiT instead of DeepDOC'''
    
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    dataset_id = "7173e3a651d911f090a802420ae90806"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # New configuration with DiT
    new_config = {
        "chunk_token_num": 128,
        "delimiter": "\n",
        "html4excel": False,
        "layout_recognize": "DiT",  # Changed from DeepDOC to DiT
        "raptor": {"use_raptor": False}
    }
    
    # Update dataset configuration
    update_url = f"{api_base_url}/api/v1/datasets/{dataset_id}"
    payload = {
        "parser_config": new_config
    }
    
    try:
        response = requests.put(update_url, headers=headers, json=payload)
        if response.status_code == 200:
            print("✅ Dataset configuration updated to use DiT")
            return True
        else:
            print(f"❌ Failed to update configuration: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

if __name__ == "__main__":
    update_dataset_config()
