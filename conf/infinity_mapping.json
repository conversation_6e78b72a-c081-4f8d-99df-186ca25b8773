{
	"id": {"type": "varchar", "default": ""},
	"doc_id": {"type": "varchar", "default": ""},
	"kb_id": {"type": "varchar", "default": ""},
	"create_time": {"type": "varchar", "default": ""},
	"create_timestamp_flt": {"type": "float", "default": 0.0},
	"img_id": {"type": "varchar", "default": ""},
	"docnm_kwd": {"type": "varchar", "default": ""},
	"title_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"title_sm_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"name_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"important_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"tag_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"important_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"question_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"question_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"content_with_weight": {"type": "varchar", "default": ""},
	"content_ltks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"content_sm_ltks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"authors_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"authors_sm_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
	"page_num_int": {"type": "varchar", "default": ""},
	"top_int": {"type": "varchar", "default": ""},
	"position_int": {"type": "varchar", "default": ""},
	"weight_int": {"type": "integer", "default": 0},
	"weight_flt": {"type": "float", "default": 0.0},
	"rank_int": {"type": "integer", "default": 0},
	"rank_flt": {"type": "float", "default": 0},
	"available_int": {"type": "integer", "default": 1},
	"knowledge_graph_kwd": {"type": "varchar", "default": ""},
	"entities_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"pagerank_fea": {"type": "integer", "default":  0},
	"tag_feas": {"type": "varchar", "default":  ""},

	"from_entity_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"to_entity_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"entity_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"entity_type_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"source_id": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
	"n_hop_with_weight": {"type": "varchar", "default": ""},
	"removed_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},

	"doc_type_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"}
}


//id: Unique identifier for the chunk
// doc_id: Reference to the original document
// kb_id: Knowledge base identifier
// content_with_weight: The actual parsed content
// content_ltks: Tokenized content for search
// page_num_int: Page number in the original document
// weight_int/weight_flt: Importance weight of the chunk
// rank_int/rank_flt: Ranking information
// Additional metadata fields for search and retrieval
 
 