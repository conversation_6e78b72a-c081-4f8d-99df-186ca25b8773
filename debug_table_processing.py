#!/usr/bin/env python3
"""
Debug Table Processing in DiT Workflow
This script shows exactly how to verify table processing is working
by adding debug logging and inspection points
"""

import sys
import os
import json
import logging
from pathlib import Path

# Add RAGFlow path
sys.path.append('/ml/shared/lazhang/code/viridoc')

from deepdoc.parser.pdf_parser import Pdf
from deepdoc.vision import LayoutRecognizer, TableStructureRecognizer

# Setup detailed logging to see table processing
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('table_processing_debug.log')
    ]
)
logger = logging.getLogger(__name__)

class TableProcessingDebugger:
    """Debug and verify table processing in DiT workflow"""
    
    def __init__(self):
        self.debug_info = {}
        
    def debug_pdf_processing(self, pdf_path):
        """Debug PDF processing with detailed table tracking"""
        print(f"\n🔍 DEBUGGING TABLE PROCESSING FOR: {pdf_path}")
        print("="*80)
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF file not found: {pdf_path}")
            return
            
        # Initialize PDF parser
        pdf_parser = Pdf()
        
        # Add debug hooks to track table processing
        original_table_job = pdf_parser._table_transformer_job
        original_layouter_call = pdf_parser.layouter.__call__
        
        def debug_table_job(ZM):
            """Debug wrapper for _table_transformer_job"""
            print(f"\n🔧 TABLE TRANSFORMER JOB STARTED")
            print(f"   📏 Zoom factor: {ZM}")
            
            # Count table regions detected by DiT
            table_count = 0
            for p, tbls in enumerate(pdf_parser.page_layout):
                page_tables = [f for f in tbls if f["type"] == "table"]
                table_count += len(page_tables)
                print(f"   📄 Page {p}: {len(page_tables)} table regions detected by DiT")
                
                for i, table in enumerate(page_tables):
                    print(f"      🔲 Table {i}: bbox={table.get('bbox', 'unknown')}, score={table.get('score', 'unknown')}")
            
            self.debug_info['dit_table_detections'] = table_count
            print(f"   📊 Total DiT table detections: {table_count}")
            
            # Call original method
            result = original_table_job(ZM)
            
            # Check table components after processing
            if hasattr(pdf_parser, 'tb_cpns'):
                print(f"   🔧 Table components after processing: {len(pdf_parser.tb_cpns)}")
                self.debug_info['table_components'] = len(pdf_parser.tb_cpns)
                
                # Show first few components
                for i, comp in enumerate(pdf_parser.tb_cpns[:5]):
                    print(f"      Component {i}: {comp.get('label', 'unknown')} at page {comp.get('pn', 'unknown')}")
            
            return result
            
        def debug_layouter_call(page_images, boxes, ZM, drop=True):
            """Debug wrapper for layouter call"""
            print(f"\n🤖 DiT LAYOUT RECOGNITION STARTED")
            print(f"   📄 Processing {len(page_images)} pages")
            
            # Call original DiT layouter
            result_boxes, page_layout = original_layouter_call(page_images, boxes, ZM, drop)
            
            # Analyze DiT results
            total_detections = 0
            table_detections = 0
            
            for page_idx, layout in enumerate(page_layout):
                page_total = len(layout)
                page_tables = len([item for item in layout if item.get('type') == 'table'])
                total_detections += page_total
                table_detections += page_tables
                
                print(f"   📄 Page {page_idx}: {page_total} total detections, {page_tables} tables")
                
                # Show table detections in detail
                for item in layout:
                    if item.get('type') == 'table':
                        print(f"      🔲 TABLE: bbox={item.get('bbox', 'unknown')}, score={item.get('score', 'unknown')}")
            
            self.debug_info['total_dit_detections'] = total_detections
            self.debug_info['dit_table_detections'] = table_detections
            
            print(f"   📊 DiT Results: {total_detections} total, {table_detections} tables")
            
            return result_boxes, page_layout
        
        # Apply debug wrappers
        pdf_parser._table_transformer_job = debug_table_job
        pdf_parser.layouter.__call__ = debug_layouter_call
        
        try:
            # Process PDF with debug tracking
            print(f"\n🚀 STARTING PDF PROCESSING...")
            sections, tables = pdf_parser(pdf_path)
            
            print(f"\n📋 PROCESSING RESULTS:")
            print(f"   📄 Sections: {len(sections)}")
            print(f"   📊 Tables: {len(tables)}")
            
            # Detailed analysis
            self.analyze_results(pdf_parser, sections, tables)
            
        except Exception as e:
            print(f"❌ Error during processing: {e}")
            logger.exception("PDF processing failed")
            
    def analyze_results(self, pdf_parser, sections, tables):
        """Analyze the processing results in detail"""
        print(f"\n🔍 DETAILED ANALYSIS:")
        print("="*50)
        
        # 1. Check DiT detections
        dit_tables = self.debug_info.get('dit_table_detections', 0)
        print(f"1. 🤖 DiT Table Detections: {dit_tables}")
        
        # 2. Check table components
        table_comps = self.debug_info.get('table_components', 0)
        print(f"2. 🔧 Table Components Generated: {table_comps}")
        
        # 3. Check final tables
        print(f"3. 📊 Final Tables Extracted: {len(tables)}")
        
        # 4. Verify the workflow
        print(f"\n✅ WORKFLOW VERIFICATION:")
        if dit_tables > 0:
            print(f"   ✅ DiT detected {dit_tables} table regions")
        else:
            print(f"   ⚠️  DiT detected no table regions")
            
        if table_comps > 0:
            print(f"   ✅ TableStructureRecognizer generated {table_comps} components")
        else:
            print(f"   ⚠️  TableStructureRecognizer generated no components")
            
        if len(tables) > 0:
            print(f"   ✅ Final extraction produced {len(tables)} tables")
        else:
            print(f"   ⚠️  Final extraction produced no tables")
            
        # 5. Show table processing chain
        print(f"\n🔗 TABLE PROCESSING CHAIN:")
        print(f"   DiT Detections → Table Cropping → Structure Recognition → Final Tables")
        print(f"   {dit_tables} → ? → {table_comps} → {len(tables)}")
        
        # 6. Check if table processing is working
        if dit_tables > 0 and table_comps > 0:
            print(f"\n🎉 TABLE PROCESSING IS WORKING!")
            print(f"   DiT detected tables → TableStructureRecognizer processed them")
        elif dit_tables > 0 and table_comps == 0:
            print(f"\n⚠️  PARTIAL ISSUE: DiT detected tables but TableStructureRecognizer didn't process them")
        elif dit_tables == 0:
            print(f"\n⚠️  NO TABLES DETECTED: Either no tables in PDF or DiT not detecting them")
        
    def create_test_with_logging(self, pdf_path):
        """Create a test that shows all the logging"""
        print(f"\n📝 CREATING DETAILED LOG TEST...")
        
        # Enable all relevant loggers
        loggers_to_enable = [
            'deepdoc.vision.layout_recognizer',
            'deepdoc.vision.table_structure_recognizer', 
            'deepdoc.parser.pdf_parser',
            'deepdoc.vision.recognizer'
        ]
        
        for logger_name in loggers_to_enable:
            logging.getLogger(logger_name).setLevel(logging.DEBUG)
            
        print(f"✅ Enabled debug logging for table processing components")
        print(f"📄 Log file: table_processing_debug.log")
        
        # Run the debug
        self.debug_pdf_processing(pdf_path)

def main():
    """Main function to run table processing debug"""
    
    # You can specify your PDF path here
    pdf_path = "/ml/shared/lazhang/data/pdfdata/sample.pdf"  # Change this to your PDF
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    
    print("🔍 TABLE PROCESSING DEBUGGER")
    print("="*50)
    print(f"This script will help you verify that:")
    print(f"1. DiT model detects table regions")
    print(f"2. Table regions are cropped correctly") 
    print(f"3. TableStructureRecognizer processes the cropped tables")
    print(f"4. Table components are generated")
    print(f"5. Final tables are extracted")
    print()
    
    debugger = TableProcessingDebugger()
    debugger.create_test_with_logging(pdf_path)
    
    print(f"\n📋 SUMMARY:")
    print(f"Check the output above and the log file 'table_processing_debug.log'")
    print(f"Look for the workflow verification to see if table processing is working")

if __name__ == "__main__":
    main()
