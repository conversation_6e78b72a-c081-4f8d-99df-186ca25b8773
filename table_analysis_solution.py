#!/usr/bin/env python3
"""
Table Analysis Solution
This script addresses the root cause of table processing issues
"""

import os
import sys
import json
import requests
from pathlib import Path
import subprocess
import time

def analyze_ragflow_logs():
    """Analyze the RAGFlow logs we found"""
    
    print("🔍 RAGFLOW LOG ANALYSIS RESULTS")
    print("="*50)
    
    print("📋 KEY FINDINGS FROM DOCKER LOGS:")
    print("   ✅ Layout Analysis: 0.20s")
    print("   ⚠️  Table Analysis: 0.00s ← THIS IS THE ISSUE")
    print("   ❗ Configuration: 'layout_recognize': 'DeepDOC'")
    print()
    
    print("🎯 ROOT CAUSE IDENTIFIED:")
    print("   1. System is using DeepDOC layout model, NOT DiT")
    print("   2. Our debug logging was added to DiT code path")
    print("   3. DeepDOC path doesn't have our debug logging")
    print("   4. Table analysis completes in 0.00s = no table processing")
    print()
    
    print("💡 EXPLANATION:")
    print("   - DeepDOC model may not be available/working")
    print("   - System falls back to minimal processing")
    print("   - Tables are not being detected or processed")
    print("   - This explains the '0 second' table analysis time")

def test_dit_vs_deepdoc():
    """Test both DiT and DeepDOC to see which works"""
    
    print(f"\n🔍 TESTING DiT vs DeepDOC")
    print("="*40)
    
    # Test DiT API
    dit_api_url = "http://mlrun-datahub.int.cgg.com/func/datahub-prod-page-seg-v2-0-0"
    
    try:
        response = requests.get(dit_api_url, timeout=10)
        dit_working = response.status_code == 200
        print(f"🤖 DiT API: {'✅ Working' if dit_working else '❌ Failed'} (Status: {response.status_code})")
    except Exception as e:
        dit_working = False
        print(f"🤖 DiT API: ❌ Failed ({e})")
    
    # Test with actual PDF
    if dit_working:
        pdf_dir = "/ml/shared/lazhang/data/pdfdata"
        if os.path.exists(pdf_dir):
            pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
            if pdf_files:
                pdf_path = os.path.join(pdf_dir, pdf_files[0])
                print(f"\n🔍 Testing DiT on: {pdf_files[0]}")
                
                table_count = test_dit_on_pdf(pdf_path, dit_api_url)
                if table_count is not None:
                    print(f"   📊 DiT detected: {table_count} tables")
                    if table_count > 0:
                        print(f"   ✅ DiT CAN detect tables in your PDFs!")
                    else:
                        print(f"   ⚠️  DiT detected 0 tables (may not contain tables)")
                else:
                    print(f"   ❌ DiT test failed")
    
    print(f"\n📋 COMPARISON:")
    print(f"   🤖 DiT: {'✅ Working' if dit_working else '❌ Not working'}")
    print(f"   🔧 DeepDOC: ❌ Not working (0.00s processing time)")
    print(f"   📊 Current Config: DeepDOC (the broken one)")
    print()
    
    if dit_working:
        print(f"💡 SOLUTION: Switch from DeepDOC to DiT!")
    else:
        print(f"⚠️  Both models have issues - need further investigation")

def test_dit_on_pdf(pdf_path, dit_api_url):
    """Test DiT API on a specific PDF and return table count"""
    
    try:
        import fitz  # PyMuPDF
        from PIL import Image
        from io import BytesIO
        import base64
        
        # Convert PDF first page to image
        doc = fitz.open(pdf_path)
        page = doc[0]
        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom
        img_data = pix.tobytes("png")
        doc.close()
        
        # Convert to PIL Image
        image = Image.open(BytesIO(img_data))
        
        # Prepare API request
        buffered = BytesIO()
        image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode()
        
        payload = {
            "inputs": {
                "image": img_base64
            }
        }
        
        # Call DiT API
        response = requests.post(dit_api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if 'outputs' in result:
                classes = result.get('outputs', {}).get('classes', [])
                scores = result.get('outputs', {}).get('scores', [])
                boxes = result.get('outputs', {}).get('boxes', [])
                
                # Count table detections
                table_count = classes.count('TABLE')
                
                # Show details if tables found
                if table_count > 0:
                    print(f"   🎯 TABLE DETECTIONS:")
                    for i, (cls, score, box) in enumerate(zip(classes, scores, boxes)):
                        if cls == 'TABLE':
                            print(f"      📊 Table {i}: score={score:.3f}, bbox={box}")
                
                return table_count
            else:
                print(f"   ❌ Invalid API response format")
                return None
        else:
            print(f"   ❌ API call failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error testing DiT: {e}")
        return None

def create_configuration_fix():
    """Create a script to fix the configuration"""
    
    print(f"\n🛠️  CONFIGURATION FIX")
    print("="*30)
    
    fix_script = """#!/usr/bin/env python3
# Configuration fix to switch from DeepDOC to DiT

import requests
import json

def update_dataset_config():
    '''Update dataset configuration to use DiT instead of DeepDOC'''
    
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    dataset_id = "7173e3a651d911f090a802420ae90806"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # New configuration with DiT
    new_config = {
        "chunk_token_num": 128,
        "delimiter": "\\n",
        "html4excel": False,
        "layout_recognize": "DiT",  # Changed from DeepDOC to DiT
        "raptor": {"use_raptor": False}
    }
    
    # Update dataset configuration
    update_url = f"{api_base_url}/api/v1/datasets/{dataset_id}"
    payload = {
        "parser_config": new_config
    }
    
    try:
        response = requests.put(update_url, headers=headers, json=payload)
        if response.status_code == 200:
            print("✅ Dataset configuration updated to use DiT")
            return True
        else:
            print(f"❌ Failed to update configuration: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

if __name__ == "__main__":
    update_dataset_config()
"""
    
    with open("fix_table_processing_config.py", "w") as f:
        f.write(fix_script)
    
    print("✅ Created fix_table_processing_config.py")
    print("💡 This script will switch your dataset from DeepDOC to DiT")

def create_test_pdf_with_tables():
    """Create a test PDF with obvious tables"""
    
    print(f"\n📄 CREATING TEST PDF WITH TABLES")
    print("="*40)
    
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors
        
        test_pdf_path = "/tmp/table_test_obvious.pdf"
        
        # Create document
        doc = SimpleDocTemplate(test_pdf_path, pagesize=letter)
        elements = []
        styles = getSampleStyleSheet()
        
        # Add title
        title = Paragraph("TABLE DETECTION TEST DOCUMENT", styles['Title'])
        elements.append(title)
        elements.append(Spacer(1, 20))
        
        # Create multiple tables with different styles
        
        # Table 1: Simple data table
        elements.append(Paragraph("Table 1: Sales Data", styles['Heading2']))
        data1 = [
            ['Product', 'Q1 Sales', 'Q2 Sales', 'Q3 Sales', 'Q4 Sales'],
            ['Widget A', '$10,000', '$12,000', '$15,000', '$18,000'],
            ['Widget B', '$8,000', '$9,500', '$11,000', '$13,500'],
            ['Widget C', '$15,000', '$16,500', '$18,000', '$20,000'],
            ['TOTAL', '$33,000', '$38,000', '$44,000', '$51,500']
        ]
        
        table1 = Table(data1)
        table1.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table1)
        elements.append(Spacer(1, 30))
        
        # Table 2: Employee table
        elements.append(Paragraph("Table 2: Employee Information", styles['Heading2']))
        data2 = [
            ['Employee ID', 'Name', 'Department', 'Salary', 'Start Date'],
            ['001', 'John Smith', 'Engineering', '$75,000', '2020-01-15'],
            ['002', 'Jane Doe', 'Marketing', '$65,000', '2019-03-22'],
            ['003', 'Bob Johnson', 'Sales', '$70,000', '2021-06-10'],
            ['004', 'Alice Brown', 'HR', '$60,000', '2018-11-05']
        ]
        
        table2 = Table(data2)
        table2.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table2)
        elements.append(Spacer(1, 30))
        
        # Table 3: Financial table
        elements.append(Paragraph("Table 3: Financial Summary", styles['Heading2']))
        data3 = [
            ['Category', 'Budget', 'Actual', 'Variance', 'Percentage'],
            ['Revenue', '$100,000', '$105,000', '+$5,000', '+5%'],
            ['Expenses', '$80,000', '$78,000', '-$2,000', '-2.5%'],
            ['Profit', '$20,000', '$27,000', '+$7,000', '+35%']
        ]
        
        table3 = Table(data3)
        table3.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.green),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 10),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 2, colors.black)
        ]))
        
        elements.append(table3)
        
        # Build PDF
        doc.build(elements)
        
        print(f"✅ Created test PDF: {test_pdf_path}")
        print(f"📊 Contains: 3 distinct tables with different styles")
        print(f"   - Table 1: 5x5 sales data with grey header")
        print(f"   - Table 2: 5x5 employee data with blue header")
        print(f"   - Table 3: 5x4 financial data with green header")
        print(f"💡 Use this PDF to test table detection after fixing config")
        
        return test_pdf_path
        
    except Exception as e:
        print(f"❌ Error creating test PDF: {e}")
        return None

def provide_action_plan():
    """Provide a clear action plan to fix table processing"""
    
    print(f"\n🎯 ACTION PLAN TO FIX TABLE PROCESSING")
    print("="*50)
    
    print("📋 STEP-BY-STEP SOLUTION:")
    print()
    
    print("1. 🔧 FIX CONFIGURATION:")
    print("   - Run: python fix_table_processing_config.py")
    print("   - This switches from DeepDOC to DiT")
    print("   - DiT is working, DeepDOC is not")
    print()
    
    print("2. 📄 TEST WITH OBVIOUS TABLES:")
    print("   - Use the generated table_test_obvious.pdf")
    print("   - Process it through RAGFlow")
    print("   - Should see table analysis > 0.00s")
    print()
    
    print("3. 🔍 VERIFY RESULTS:")
    print("   - Check Docker logs: docker logs --tail 50 ragflow-server")
    print("   - Look for 'Table analysis (X.XXs)' where X > 0")
    print("   - Check chunk count increase")
    print()
    
    print("4. 📊 REPROCESS EXISTING PDFs:")
    print("   - After config fix, reprocess your original PDFs")
    print("   - Should now see proper table processing")
    print("   - Table analysis time should be > 0.00s")
    print()
    
    print("🎯 EXPECTED RESULTS AFTER FIX:")
    print("   ✅ Table analysis: > 0.00s (instead of 0.00s)")
    print("   ✅ DiT detections in debug output")
    print("   ✅ Table components generated")
    print("   ✅ Increased chunk counts for table-heavy documents")

def main():
    """Main analysis and solution function"""
    
    print("🔍 TABLE ANALYSIS COMPREHENSIVE SOLUTION")
    print("="*80)
    print("Based on RAGFlow Docker logs analysis")
    print()
    
    # Step 1: Analyze what we found
    analyze_ragflow_logs()
    
    # Step 2: Test DiT vs DeepDOC
    test_dit_vs_deepdoc()
    
    # Step 3: Create configuration fix
    create_configuration_fix()
    
    # Step 4: Create test PDF
    test_pdf = create_test_pdf_with_tables()
    
    # Step 5: Provide action plan
    provide_action_plan()
    
    print(f"\n📋 SUMMARY")
    print("="*20)
    print(f"🎯 Root Cause: Using DeepDOC (broken) instead of DiT (working)")
    print(f"🛠️  Solution: Switch configuration to DiT")
    print(f"📄 Test PDF: {'✅ Created' if test_pdf else '❌ Failed'}")
    print(f"🔧 Fix Script: ✅ Created (fix_table_processing_config.py)")
    print()
    print(f"🚀 NEXT: Run the fix script and test with the new PDF!")

if __name__ == "__main__":
    main()
