#!/usr/bin/env python3


import os
import json
import time
import requests
import glob
from datetime import datetime
from requests_toolbelt.multipart.encoder import MultipartEncoder

class RAGFlowAPIClient:
    """RAGFlow API client with all critical fixes applied"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir):
        """Save output from each processing step"""
        filename = f"{step_name}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"  💾 Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"  ⚠️  Failed to save {step_name} output: {str(e)}")
            return None
    
    def create_dataset(self, dataset_name):
        """Step 1: Create a dataset (correct response path: .data.id)"""
        print(f"1. Creating dataset: {dataset_name}")
        url = f"{self.api_base_url}/api/v1/datasets"
        
        payload = {
            "name": dataset_name,
            "description": f"Dataset for {dataset_name}",
            "chunk_method": "naive",
            "parser_config": {
                "chunk_token_num": 512,
                "layout_recognize": "DeepDoc"
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"  ✅ Dataset created: {dataset_id}")
                return dataset_id
            else:
                print(f"   Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  Request failed: {str(e)}")
            return None
    
    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF (CORRECTED: uses 'files' field)"""
        print(f"2. Uploading: {os.path.basename(pdf_path)}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        
        try:
            # CORRECTED: Uses 'files' parameter (plural)
            multipart_data = MultipartEncoder(
                fields={'files': (os.path.basename(pdf_path), open(pdf_path, 'rb'), 'application/pdf')}
            )
            
            # Use separate request (can't use session with multipart)
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': multipart_data.content_type
            }
            response = requests.post(url, data=multipart_data, headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                # CORRECTED PATH: .data.documents[0].id
                document_id = result["data"]["documents"][0]["id"]
                print(f"  ✅ Document uploaded: {document_id}")
                return document_id
            else:
                print(f"  Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  Upload failed: {str(e)}")
            return None
        finally:
            if 'multipart_data' in locals():
                multipart_data.close()
    
    def parse_documents(self, dataset_id, document_id):
        """Step 3: Parse documents (correct payload format)"""
        print(f"3. Parsing document: {document_id}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        payload = {
            "document_ids": [document_id]  # Array of document IDs
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                print("  ✅ Parsing initiated")
                return True
            else:
                print(f"  Failed: {result.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            print(f"  Parse request failed: {str(e)}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, timeout=600, interval=10):
        """Step 4: Wait for completion (CORRECTED status: 'indexed')"""
        print(f"4. Waiting for completion (timeout: {timeout}s)...")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                response.raise_for_status()
                result = response.json()
                
                if result.get("code") == 0:
                    doc_data = result["data"]
                    status = doc_data.get("status", "unknown")
                    progress = doc_data.get("progress", 0)
                    
                    print(f"  📊 Status: {status}, Progress: {progress:.0%}")
                    
                    # CORRECTED STATUS CHECK
                    if status == "indexed":
                        print("  ✅ Processing completed!")
                        return True, doc_data
                    elif status == "error":
                        print(f"   Processing failed: {doc_data.get('progress_msg', 'Unknown error')}")
                        return False, doc_data
                
                time.sleep(interval)
            except Exception as e:
                print(f"  ⚠️  Status check failed: {str(e)}")
                time.sleep(interval)
        
        print(f" Timeout after {timeout}s")
        return False, None
    
    def retrieve_chunks(self, dataset_id, document_id):
        """Step 5: Retrieve chunks (CORRECTED: uses 'document_id' param)"""
        print(f"5. Retrieving chunks for document: {document_id}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        # CORRECTED PARAMETER NAME (singular)
        params = {"document_id": document_id}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                chunks = result["data"].get("chunks", [])
                print(f"  ✅ Retrieved {len(chunks)} chunks")
                return chunks
            else:
                print(f"   Failed: {result.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            print(f"   Retrieve failed: {str(e)}")
            return []

def process_pdf(pdf_path, output_base, api_base_url, api_key):
    """Process a single PDF through RAGFlow API"""
    client = RAGFlowAPIClient(api_base_url, api_key)
    filename = os.path.basename(pdf_path)
    
    # Create output directory
    output_dir = client.create_output_directory(output_base, filename)
    print(f"📁 Output directory: {output_dir}")
    
    # Generate dataset name
    dataset_name = f"{os.path.splitext(filename)[0]}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    try:
        # Step 1: Create dataset
        dataset_id = client.create_dataset(dataset_name)
        if not dataset_id:
            return []
        client.save_step_output({"dataset_id": dataset_id}, "01_dataset", output_dir)
        
        # Step 2: Upload document
        doc_id = client.upload_document(dataset_id, pdf_path)
        if not doc_id:
            return []
        client.save_step_output({"document_id": doc_id}, "02_upload", output_dir)
        
        # Step 3: Parse document
        if not client.parse_documents(dataset_id, doc_id):
            return []
        client.save_step_output({"parse_initiated": True}, "03_parse", output_dir)
        
        # Step 4: Wait for completion
        success, doc_info = client.wait_for_parsing_completion(dataset_id, doc_id)
        client.save_step_output(doc_info, "04_status", output_dir)
        if not success:
            return []
        
        # Step 5: Retrieve chunks
        chunks = client.retrieve_chunks(dataset_id, doc_id)
        client.save_step_output(chunks, "05_chunks", output_dir)
        
        # Format and save readable chunks
        readable_chunks = []
        for i, chunk in enumerate(chunks):
            readable_chunks.append({
                "chunk_id": i+1,
                "content": chunk.get("content", ""),
                "page": chunk.get("position_info", {}).get("page", 0),
                "keywords": chunk.get("important_keywords", [])
            })
        
        client.save_step_output(readable_chunks, "06_readable_chunks", output_dir)
        
        # Save processing summary
        summary = {
            "pdf_file": filename,
            "dataset_id": dataset_id,
            "document_id": doc_id,
            "chunk_count": len(chunks),
            "processing_time": datetime.now().isoformat(),
            "status": "success"
        }
        client.save_step_output(summary, "07_summary", output_dir)
        
        print(f"✅ Processing completed! Chunks: {len(chunks)}")
        return chunks
        
    except Exception as e:
        print(f"Processing failed: {str(e)}")
        client.save_step_output({"error": str(e)}, "error", output_dir)
        return []

def process_pdf_folder(folder_path, output_base, api_base_url, api_key):
    """Process all PDFs in a folder"""
    print(f"🚀 Starting batch processing of PDFs in: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))
    
    if not pdf_files:
        print(" No PDF files found")
        return []
    
    print(f"📄 Found {len(pdf_files)} PDF files")
    results = []
    
    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")
        chunks = process_pdf(pdf_path, output_base, api_base_url, api_key)
        results.append({
            "file": filename,
            "chunks_count": len(chunks),
            "status": "success" if chunks else "failed"
        })
    
    # Save batch summary
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = os.path.join(output_base, f"batch_summary_{timestamp}.json")
    with open(summary_path, 'w') as f:
        json.dump({
            "total_files": len(pdf_files),
            "processed": len([r for r in results if r["status"] == "success"]),
            "failed": len([r for r in results if r["status"] == "failed"]),
            "results": results
        }, f, indent=2)
    
    print(f"\n🎉 Batch processing complete! Summary saved to: {summary_path}")
    return results

def main():
    # Configuration - USE ENVIRONMENT VARIABLES IN PRODUCTION!
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj") 
    
    # Path configuration (replace with your paths)
    PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
    OUTPUT_BASE = r"/ml/shared/lazhang/data"
    
    # Process all PDFs in folder
    process_pdf_folder(PDF_FOLDER, OUTPUT_BASE, API_BASE_URL, API_KEY)

if __name__ == "__main__":
    main()