#!/usr/bin/env python3


import os
import json
import time
import requests
import glob
from datetime import datetime

class RAGFlowAPIClient:
    """RAGFlow API client with all critical fixes applied"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir):
        """Save output from each processing step"""
        filename = f"{step_name}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"   Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"   Failed to save {step_name} output: {str(e)}")
            return None
    
    def create_dataset(self, dataset_name):
        """Step 1: Create a dataset (correct response path: .data.id)"""
        print(f"1. Creating dataset: {dataset_name}")
        url = f"{self.api_base_url}/api/v1/datasets"
        
        payload = {
            "name": dataset_name,
            "description": f"Dataset for {dataset_name}",
            "chunk_method": "General",
            "parser_config": {
                "chunk_token_num": 512,
                "layout_recognize": "DeepDOC"
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"   Dataset created: {dataset_id}")
                return dataset_id
            else:
                print(f"   Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  Request failed: {str(e)}")
            return None

    def create_or_find_dataset(self, dataset_name):
        """Create dataset or find existing one"""
        # First try to create the dataset
        dataset_id = self.create_dataset(dataset_name)
        if dataset_id:
            return dataset_id

        # If creation failed, try to find existing dataset
        print(f"   Searching for existing dataset: {dataset_name}")
        try:
            url = f"{self.api_base_url}/api/v1/datasets"
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                # Handle different possible response formats
                data = result["data"]
                if isinstance(data, list):
                    datasets = data
                else:
                    datasets = data.get("datasets", [])

                for dataset in datasets:
                    if dataset.get("name") == dataset_name:
                        dataset_id = dataset.get("id")
                        print(f"   Found existing dataset: {dataset_id}")
                        return dataset_id

                # If not found, create with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_name = f"{dataset_name}_{timestamp}"
                print(f"  🔄 Creating new dataset with timestamp: {new_name}")
                return self.create_dataset(new_name)
            else:
                print(f"  ❌ Failed to list datasets: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  ❌ Error searching for dataset: {str(e)}")
            return None

    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF (WORKING: uses 'file' field name)"""
        print(f"2. Uploading: {os.path.basename(pdf_path)}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"

        try:
            with open(pdf_path, 'rb') as file_handle:
                files = {'file': (os.path.basename(pdf_path), file_handle, 'application/pdf')}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                response = requests.post(url, files=files, headers=headers)
                response.raise_for_status()
                result = response.json()

                if result.get("code") == 0:
                    # Handle response format: data is a list with document info
                    data = result.get("data", [])
                    if isinstance(data, list) and len(data) > 0:
                        document_id = data[0].get("id")
                        print(f"  ✅ Document uploaded: {document_id}")
                        return document_id
                    else:
                        print(f"  Failed: Unexpected response format")
                        return None
                else:
                    print(f"  Failed: {result.get('message', 'Unknown error')}")
                    return None
        except Exception as e:
            print(f"  Upload failed: {str(e)}")
            return None
    
    def parse_documents(self, dataset_id, document_id):
        """Step 3: Parse documents (correct payload format)"""
        print(f"3. Parsing document: {document_id}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        payload = {
            "document_ids": [document_id]  # Array of document IDs
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                print("   Parsing initiated")
                return True
            else:
                print(f"  Failed: {result.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            print(f"  Parse request failed: {str(e)}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, timeout=600, interval=10):
        """Step 4: Wait for completion (CORRECTED status: 'indexed')"""
        print(f"4. Waiting for completion (timeout: {timeout}s)...")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                response.raise_for_status()

                # Handle empty response
                if not response.text.strip():
                    print(f"   Empty response, retrying...")
                    time.sleep(interval)
                    continue

                result = response.json()
                print(f"   Debug - Status response: {result}")

                if result.get("code") == 0:
                    doc_data = result["data"]
                    status = doc_data.get("status", "unknown")
                    progress = doc_data.get("progress", 0)

                    print(f"   Status: {status}, Progress: {progress:.0%}")

                    # CORRECTED STATUS CHECK
                    if status == "indexed":
                        print("   Processing completed!")
                        return True, doc_data
                    elif status == "error":
                        print(f"   Processing failed: {doc_data.get('progress_msg', 'Unknown error')}")
                        return False, doc_data
                else:
                    print(f"   API error: {result.get('message', 'Unknown error')}")

                time.sleep(interval)
            except requests.exceptions.JSONDecodeError as e:
                print(f"   JSON decode error (empty response?): {str(e)}")
                time.sleep(interval)
            except Exception as e:
                print(f"   Status check failed: {str(e)}")
                time.sleep(interval)

        print(f" Timeout after {timeout}s")
        return False, None
    
    def retrieve_chunks(self, dataset_id, document_id=None):
        """Step 5: Retrieve chunks - try different API approaches"""
        print(f"5. Retrieving chunks for dataset: {dataset_id}")

        # Try approach 1: GET chunks with document_id parameter
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
            params = {"document_id": document_id} if document_id else {}

            response = self.session.get(url, params=params)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                if isinstance(data, dict):
                    chunks = data.get("chunks", [])
                elif isinstance(data, list):
                    chunks = data
                else:
                    chunks = []
                print(f"   ✅ Retrieved {len(chunks)} chunks")
                return chunks
            else:
                print(f"   Approach 1 failed: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"   Approach 1 error: {str(e)}")

        # Try approach 2: POST to chunks endpoint
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
            payload = {"document_ids": [document_id]} if document_id else {}

            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                if isinstance(data, dict):
                    chunks = data.get("chunks", [])
                elif isinstance(data, list):
                    chunks = data
                else:
                    chunks = []
                print(f"   ✅ Retrieved {len(chunks)} chunks (approach 2)")
                return chunks
            else:
                print(f"   Approach 2 failed: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"   Approach 2 error: {str(e)}")

        # Try approach 3: Get all chunks from dataset
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"

            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                if isinstance(data, dict):
                    chunks = data.get("chunks", [])
                elif isinstance(data, list):
                    chunks = data
                else:
                    chunks = []
                print(f"   ✅ Retrieved {len(chunks)} chunks (all from dataset)")
                return chunks
            else:
                print(f"   Approach 3 failed: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"   Approach 3 error: {str(e)}")

        print(f"   ❌ All chunk retrieval approaches failed")
        return []

def process_pdf(pdf_path, output_base, api_base_url, api_key):
    """Process a single PDF through RAGFlow API"""
    client = RAGFlowAPIClient(api_base_url, api_key)
    filename = os.path.basename(pdf_path)
    
    # Create output directory
    output_dir = client.create_output_directory(output_base, filename)
    print(f" Output directory: {output_dir}")
    
    # Generate dataset name
    dataset_name = f"{os.path.splitext(filename)[0]}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    try:
        # Step 1: Create dataset
        dataset_id = client.create_dataset(dataset_name)
        if not dataset_id:
            return []
        client.save_step_output({"dataset_id": dataset_id}, "01_dataset", output_dir)
        
        # Step 2: Upload document
        doc_id = client.upload_document(dataset_id, pdf_path)
        if not doc_id:
            return []
        client.save_step_output({"document_id": doc_id}, "02_upload", output_dir)
        
        # Step 3: Parse document
        if not client.parse_documents(dataset_id, doc_id):
            return []
        client.save_step_output({"parse_initiated": True}, "03_parse", output_dir)
        
        # Step 4: Wait for completion
        success, doc_info = client.wait_for_parsing_completion(dataset_id, doc_id)
        client.save_step_output(doc_info, "04_status", output_dir)
        if not success:
            return []
        
        # Step 5: Retrieve chunks
        chunks = client.retrieve_chunks(dataset_id, doc_id)
        client.save_step_output(chunks, "05_chunks", output_dir)
        
        # Format and save readable chunks
        readable_chunks = []
        for i, chunk in enumerate(chunks):
            readable_chunks.append({
                "chunk_id": i+1,
                "content": chunk.get("content", ""),
                "page": chunk.get("position_info", {}).get("page", 0),
                "keywords": chunk.get("important_keywords", [])
            })
        
        client.save_step_output(readable_chunks, "06_readable_chunks", output_dir)
        
        # Save processing summary
        summary = {
            "pdf_file": filename,
            "dataset_id": dataset_id,
            "document_id": doc_id,
            "chunk_count": len(chunks),
            "processing_time": datetime.now().isoformat(),
            "status": "success"
        }
        client.save_step_output(summary, "07_summary", output_dir)
        
        print(f" Processing completed! Chunks: {len(chunks)}")
        return chunks
        
    except Exception as e:
        print(f"Processing failed: {str(e)}")
        client.save_step_output({"error": str(e)}, "error", output_dir)
        return []

def process_pdf_folder_single_dataset(folder_path, output_base, api_base_url, api_key, dataset_name="testrag"):
    """Process all PDFs in a folder using a single shared dataset"""
    print(f"🚀 Starting batch processing of PDFs in: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print(" No PDF files found")
        return []

    print(f"📄 Found {len(pdf_files)} PDF files")
    print(f"📦 Using single dataset: {dataset_name}")

    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create shared output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_base, f"{dataset_name}_batch_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f" Batch output directory: {output_dir}")

    # Step 1: Create single dataset for all PDFs
    print(f"\n1. Creating shared dataset: {dataset_name}")
    dataset_id = client.create_or_find_dataset(dataset_name)
    if not dataset_id:
        print("❌ Failed to create or find dataset")
        return []

    client.save_step_output({"dataset_id": dataset_id, "dataset_name": dataset_name}, "01_dataset", output_dir)
    print(f"   Shared dataset created: {dataset_id}")

    # Step 2: Process each PDF
    results = []
    all_chunks = []
    document_ids = []

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")

        try:
            # Upload document to shared dataset
            doc_id = client.upload_document(dataset_id, pdf_path)
            if not doc_id:
                results.append({"file": filename, "status": "upload_failed", "chunks_count": 0})
                continue

            document_ids.append({"file": filename, "document_id": doc_id})
            print(f"   Document uploaded: {doc_id}")

            # Parse document
            if not client.parse_documents(dataset_id, doc_id):
                results.append({"file": filename, "status": "parse_failed", "chunks_count": 0})
                continue

            # Wait for completion (simplified approach)
            print(f"  ⏳ Waiting for processing to complete...")
            time.sleep(30)  # Wait 30 seconds for processing

            # Retrieve chunks
            chunks = client.retrieve_chunks(dataset_id, doc_id)
            all_chunks.extend(chunks)

            results.append({
                "file": filename,
                "document_id": doc_id,
                "chunks_count": len(chunks),
                "status": "success"
            })

            print(f"   {filename} processed successfully! Chunks: {len(chunks)}")

        except Exception as e:
            print(f"   Failed to process {filename}: {str(e)}")
            results.append({"file": filename, "status": "error", "chunks_count": 0, "error": str(e)})

    # Save all results
    client.save_step_output(document_ids, "02_document_ids", output_dir)
    client.save_step_output(all_chunks, "03_all_chunks", output_dir)

    # Format and save readable chunks
    readable_chunks = []
    for i, chunk in enumerate(all_chunks):
        readable_chunks.append({
            "chunk_id": i+1,
            "content": chunk.get("content", ""),
            "page": chunk.get("position_info", {}).get("page", 0),
            "keywords": chunk.get("important_keywords", []),
            "document_name": chunk.get("document_name", "unknown")
        })

    client.save_step_output(readable_chunks, "04_readable_chunks", output_dir)

    # Save processing summary
    summary = {
        "dataset_name": dataset_name,
        "dataset_id": dataset_id,
        "total_files": len(pdf_files),
        "processed": len([r for r in results if r["status"] == "success"]),
        "failed": len([r for r in results if r["status"] != "success"]),
        "total_chunks": len(all_chunks),
        "processing_time": datetime.now().isoformat(),
        "results": results
    }
    client.save_step_output(summary, "05_summary", output_dir)

    print(f"\n Batch processing complete!")
    print(f" Dataset: {dataset_name} (ID: {dataset_id})")
    print(f" Files processed: {summary['processed']}/{summary['total_files']}")
    print(f" Total chunks: {summary['total_chunks']}")
    print(f" Results saved to: {output_dir}")

    return results

def process_pdf_folder(folder_path, output_base, api_base_url, api_key):
    """Process all PDFs in a folder (legacy function - creates separate datasets)"""
    print(f"🚀 Starting batch processing of PDFs in: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print(" No PDF files found")
        return []

    print(f"Found {len(pdf_files)} PDF files")
    results = []

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")
        chunks = process_pdf(pdf_path, output_base, api_base_url, api_key)
        results.append({
            "file": filename,
            "chunks_count": len(chunks),
            "status": "success" if chunks else "failed"
        })

    # Save batch summary
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = os.path.join(output_base, f"batch_summary_{timestamp}.json")
    with open(summary_path, 'w') as f:
        json.dump({
            "total_files": len(pdf_files),
            "processed": len([r for r in results if r["status"] == "success"]),
            "failed": len([r for r in results if r["status"] == "failed"]),
            "results": results
        }, f, indent=2)

    print(f"\n🎉 Batch processing complete! Summary saved to: {summary_path}")
    return results

def main():
    # Configuration - USE ENVIRONMENT VARIABLES IN PRODUCTION!
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")

    # Path configuration (replace with your paths)
    PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
    OUTPUT_BASE = r"/ml/shared/lazhang/data"

    # Process all PDFs in folder using single dataset named "testrag"
    process_pdf_folder_single_dataset(PDF_FOLDER, OUTPUT_BASE, API_BASE_URL, API_KEY, "testrag")

if __name__ == "__main__":
    main()