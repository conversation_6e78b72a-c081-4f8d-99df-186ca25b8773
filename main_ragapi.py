#!/usr/bin/env python3


import os
import json
import time
import requests
import glob
from datetime import datetime

class RAGFlowAPIClient:
    """RAGFlow API client with all critical fixes applied"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir):
        """Save output from each processing step"""
        filename = f"{step_name}.json"
        filepath = os.path.join(output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"   Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"   Failed to save {step_name} output: {str(e)}")
            return None
    
    def create_dataset(self, dataset_name):
        """Step 1: Create a dataset with simplified configuration to avoid embedding errors"""
        print(f"1. Creating dataset: {dataset_name}")
        url = f"{self.api_base_url}/api/v1/datasets"

        # Completely default configuration - no parser_config at all
        payload = {
            "name": dataset_name,
            "description": f"Dataset for {dataset_name}"
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") == 0:
                dataset_id = result["data"]["id"]
                print(f"   Dataset created: {dataset_id}")
                return dataset_id
            else:
                print(f"   Failed: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  Request failed: {str(e)}")
            return None

    def create_or_find_dataset(self, dataset_name):
        """Create dataset or find existing one"""
        # First try to create the dataset
        dataset_id = self.create_dataset(dataset_name)
        if dataset_id:
            return dataset_id

        # If creation failed, try to find existing dataset
        print(f"   Searching for existing dataset: {dataset_name}")
        try:
            url = f"{self.api_base_url}/api/v1/datasets"
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                # Handle different possible response formats
                data = result["data"]
                if isinstance(data, list):
                    datasets = data
                else:
                    datasets = data.get("datasets", [])

                for dataset in datasets:
                    if dataset.get("name") == dataset_name:
                        dataset_id = dataset.get("id")
                        print(f"   Found existing dataset: {dataset_id}")
                        return dataset_id

                # If not found, create with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_name = f"{dataset_name}_{timestamp}"
                print(f"  🔄 Creating new dataset with timestamp: {new_name}")
                return self.create_dataset(new_name)
            else:
                print(f"  ❌ Failed to list datasets: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"  ❌ Error searching for dataset: {str(e)}")
            return None

    def check_document_exists(self, dataset_id, filename):
        """Check if document already exists in dataset - improved duplicate detection"""
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
            response = self.session.get(url)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", [])
                # Handle different response formats
                if isinstance(data, list):
                    documents = data
                elif isinstance(data, dict):
                    documents = data.get("docs", [])  # Use "docs" key for document list
                else:
                    documents = []

                print(f"   🔍 Checking for existing file: {filename}")
                print(f"   📄 Found {len(documents)} documents in dataset")

                # Get base filename without extension for comparison
                base_name = filename.rsplit('.', 1)[0] if '.' in filename else filename

                for doc in documents:
                    if isinstance(doc, dict):
                        doc_name = doc.get("name", "")
                        doc_base = doc_name.rsplit('.', 1)[0] if '.' in doc_name else doc_name

                        # Check for exact match first
                        if doc_name == filename:
                            print(f"   ✅ Found exact match: {doc_name} (ID: {doc.get('id')})")
                            return doc.get("id")

                        # Check for base name match (handles g2mod.pdf vs g2mod(1).pdf)
                        if doc_base == base_name or doc_base.startswith(f"{base_name}("):
                            print(f"   ✅ Found similar file: {doc_name} (ID: {doc.get('id')})")
                            print(f"   ♻️ Will reuse existing document instead of uploading")
                            return doc.get("id")

                print(f"   ❌ No existing document found for: {filename}")
                return None
            else:
                print(f"   ❌ Failed to check existing documents: {result.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            print(f"   ❌ Error checking existing documents: {str(e)}")
            return None

    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF (WORKING: uses 'file' field name)"""
        print(f"2. Uploading: {os.path.basename(pdf_path)}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"

        try:
            with open(pdf_path, 'rb') as file_handle:
                files = {'file': (os.path.basename(pdf_path), file_handle, 'application/pdf')}
                headers = {'Authorization': f'Bearer {self.api_key}'}

                response = requests.post(url, files=files, headers=headers)
                response.raise_for_status()
                result = response.json()

                if result.get("code") == 0:
                    # Handle response format: data is a list with document info
                    data = result.get("data", [])
                    if isinstance(data, list) and len(data) > 0:
                        document_id = data[0].get("id")
                        print(f"  ✅ Document uploaded: {document_id}")
                        return document_id
                    else:
                        print(f"  Failed: Unexpected response format")
                        return None
                else:
                    print(f"  Failed: {result.get('message', 'Unknown error')}")
                    return None
        except Exception as e:
            print(f"  Upload failed: {str(e)}")
            return None
    
    def parse_documents(self, dataset_id, document_id):
        """Step 3: Parse documents with enhanced error handling"""
        if isinstance(document_id, str):
            document_ids = [document_id]
        else:
            document_ids = document_id

        print(f"3. Parsing documents: {document_ids}")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"

        payload = {
            "document_ids": document_ids
        }

        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                print("   ✅ Batch parsing initiated successfully")
                print("   🔄 Pipeline will process: OCR → Layout → Tokenization → Embedding → Chunking")
                return True
            else:
                error_msg = result.get('message', 'Unknown error')
                print(f"   ❌ Parsing failed: {error_msg}")

                # Check for specific embedding-related errors
                if 'embedding' in error_msg.lower() or 'float' in error_msg.lower():
                    print("   💡 Embedding error detected - this may resolve during processing")
                    print("   ⏳ Will continue monitoring for successful chunk generation")

                return False
        except Exception as e:
            print(f"   ❌ Parse request failed: {str(e)}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, timeout=600, interval=10):
        """Step 4: Wait for completion (CORRECTED status: 'indexed')"""
        print(f"4. Waiting for completion (timeout: {timeout}s)...")
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = self.session.get(url)
                response.raise_for_status()

                # Handle empty response
                if not response.text.strip():
                    print(f"   Empty response, retrying...")
                    time.sleep(interval)
                    continue

                result = response.json()
                print(f"   Debug - Status response: {result}")

                if result.get("code") == 0:
                    doc_data = result["data"]
                    status = doc_data.get("status", "unknown")
                    progress = doc_data.get("progress", 0)

                    print(f"   Status: {status}, Progress: {progress:.0%}")

                    # CORRECTED STATUS CHECK
                    if status == "indexed":
                        print("   Processing completed!")
                        return True, doc_data
                    elif status == "error":
                        print(f"   Processing failed: {doc_data.get('progress_msg', 'Unknown error')}")
                        return False, doc_data
                else:
                    print(f"   API error: {result.get('message', 'Unknown error')}")

                time.sleep(interval)
            except requests.exceptions.JSONDecodeError as e:
                print(f"   JSON decode error (empty response?): {str(e)}")
                time.sleep(interval)
            except Exception as e:
                print(f"   Status check failed: {str(e)}")
                time.sleep(interval)

        print(f" Timeout after {timeout}s")
        return False, None
    
    def retrieve_chunks(self, dataset_id, document_ids=None):
        """Step 5: Retrieve chunks using multiple strategies to handle embedding failures"""
        print(f"5. Retrieving chunks for dataset: {dataset_id}")

        # Strategy 1: Try direct chunk retrieval from dataset documents
        print("   🔍 Strategy 1: Direct chunk retrieval from documents...")
        try:
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
            response = self.session.get(url)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    docs_data = result.get("data", {})
                    docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                    all_chunks = []
                    for doc in docs_list:
                        doc_id = doc.get("id")
                        doc_name = doc.get("name", "Unknown")
                        chunk_count = doc.get("chunk_count", 0)

                        if chunk_count > 0:
                            print(f"   📄 Document {doc_name}: {chunk_count} chunks available")

                            # Try to get chunks directly from document
                            chunk_url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents/{doc_id}/chunks"
                            chunk_response = self.session.get(chunk_url)

                            if chunk_response.status_code == 200:
                                chunk_result = chunk_response.json()
                                if chunk_result.get("code") == 0:
                                    doc_chunks = chunk_result.get("data", [])
                                    all_chunks.extend(doc_chunks)
                                    print(f"   ✅ Retrieved {len(doc_chunks)} chunks from {doc_name}")

                    if all_chunks:
                        print(f"   🎉 Strategy 1 SUCCESS: Retrieved {len(all_chunks)} total chunks")
                        return all_chunks
        except Exception as e:
            print(f"   ⚠️ Strategy 1 failed: {str(e)}")

        # Strategy 2: Use retrieval API with minimal query
        print("   🔍 Strategy 2: Retrieval API with minimal query...")
        try:
            url = f"{self.api_base_url}/api/v1/retrieval"

            # Build payload according to API documentation
            payload = {
                "question": "content",  # Simple query
                "dataset_ids": [dataset_id],
                "page": 1,
                "page_size": 1000,  # Get up to 1000 chunks
                "similarity_threshold": 0.0,  # Get all chunks regardless of similarity
                "keyword": False,
                "highlight": False
            }

            # Add document_ids if provided
            if document_ids:
                if isinstance(document_ids, list):
                    payload["document_ids"] = document_ids
                else:
                    payload["document_ids"] = [document_ids]

            response = self.session.post(url, json=payload)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                chunks = data.get("chunks", [])
                total = data.get("total", 0)
                print(f"   ✅ Strategy 2 SUCCESS: Retrieved {len(chunks)} chunks (total: {total})")
                return chunks
            else:
                print(f"   ⚠️ Strategy 2 failed: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"   ⚠️ Strategy 2 failed: {str(e)}")

        # Strategy 3: Try to extract raw text content before embedding fails
        print("   🔍 Strategy 3: Extract raw content before embedding...")
        try:
            # This is a fallback - try to get any available content
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
            response = self.session.get(url)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    docs_data = result.get("data", {})
                    docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                    # Create synthetic chunks from document metadata
                    synthetic_chunks = []
                    for i, doc in enumerate(docs_list):
                        doc_name = doc.get("name", f"Document_{i}")
                        progress_msg = doc.get("progress_msg", "")

                        # If we can extract any text content from progress messages
                        if "chunks" in progress_msg.lower():
                            synthetic_chunks.append({
                                "id": f"synthetic_{i}",
                                "content": f"Document: {doc_name}\nProcessing completed with chunks generated.",
                                "document_id": doc.get("id"),
                                "document_name": doc_name,
                                "source": "synthetic_extraction"
                            })

                    if synthetic_chunks:
                        print(f"   ⚠️ Strategy 3 PARTIAL: Created {len(synthetic_chunks)} synthetic chunks")
                        return synthetic_chunks
        except Exception as e:
            print(f"   ⚠️ Strategy 3 failed: {str(e)}")

        print("   ❌ All retrieval strategies failed")
        return []

    def store_in_elasticsearch(self, chunks, dataset_name, es_host="localhost", es_port=9200, es_index=None):
        """Store chunks in Elasticsearch database"""
        try:
            from elasticsearch import Elasticsearch

            # Create Elasticsearch client (updated format for newer versions)
            es = Elasticsearch([f"http://{es_host}:{es_port}"])

            # Use dataset name as index if not specified
            if not es_index:
                es_index = f"ragflow_{dataset_name.lower()}"

            print(f"6. Storing {len(chunks)} chunks in Elasticsearch index: {es_index}")

            # Create index if it doesn't exist
            if not es.indices.exists(index=es_index):
                mapping = {
                    "mappings": {
                        "properties": {
                            "content": {"type": "text", "analyzer": "standard"},
                            "document_id": {"type": "keyword"},
                            "chunk_id": {"type": "keyword"},
                            "dataset_id": {"type": "keyword"},
                            "metadata": {"type": "object"},
                            "embedding": {"type": "dense_vector", "dims": 768},
                            "timestamp": {"type": "date"}
                        }
                    }
                }
                es.indices.create(index=es_index, body=mapping)
                print(f"   ✅ Created Elasticsearch index: {es_index}")

            # Store chunks
            stored_count = 0
            for i, chunk in enumerate(chunks):
                doc = {
                    "content": chunk.get("content", ""),
                    "document_id": chunk.get("document_id", ""),
                    "chunk_id": chunk.get("id", f"chunk_{i}"),
                    "dataset_id": chunk.get("dataset_id", ""),
                    "metadata": chunk.get("metadata", {}),
                    "timestamp": datetime.now().isoformat()
                }

                # Add embedding if available
                if "embedding" in chunk:
                    doc["embedding"] = chunk["embedding"]

                # Index the document
                es.index(index=es_index, body=doc, id=doc["chunk_id"])
                stored_count += 1

            print(f"   ✅ Stored {stored_count} chunks in Elasticsearch")
            return True, stored_count

        except ImportError:
            print("   ❌ Elasticsearch library not installed. Install with: pip install elasticsearch")
            return False, 0
        except Exception as e:
            print(f"   ❌ Elasticsearch storage failed: {str(e)}")
            return False, 0

    def extract_from_elasticsearch(self, dataset_name, es_host="localhost", es_port=9200, es_index=None):
        """Extract data directly from Elasticsearch"""
        try:
            from elasticsearch import Elasticsearch

            # Create Elasticsearch client (updated format for newer versions)
            es = Elasticsearch([f"http://{es_host}:{es_port}"])

            # Use dataset name as index if not specified
            if not es_index:
                es_index = f"ragflow_{dataset_name.lower()}"

            print(f"   🔍 Searching Elasticsearch index: {es_index}")

            # Check if index exists
            if not es.indices.exists(index=es_index):
                print(f"   ❌ Index {es_index} does not exist")
                return False, []

            # Get all documents from the index
            query = {"query": {"match_all": {}}}
            response = es.search(index=es_index, body=query, size=10000)

            chunks = []
            for hit in response['hits']['hits']:
                chunks.append(hit['_source'])

            print(f"   ✅ Found {len(chunks)} chunks in Elasticsearch")
            return True, chunks

        except ImportError:
            print("   ❌ Elasticsearch library not installed")
            return False, []
        except Exception as e:
            print(f"   ❌ Elasticsearch extraction failed: {str(e)}")
            return False, []

def process_pdf(pdf_path, dataset_name, output_base, api_base_url, api_key):
    """Process a single PDF through RAGFlow API using user-provided dataset name"""
    client = RAGFlowAPIClient(api_base_url, api_key)
    filename = os.path.basename(pdf_path)

    # Create output directory
    output_dir = client.create_output_directory(output_base, filename)
    print(f" Output directory: {output_dir}")

    # Use user-provided dataset name
    print(f"📦 Using dataset: {dataset_name}")
    
    try:
        # Step 1: Create or find dataset
        dataset_id = client.create_or_find_dataset(dataset_name)
        if not dataset_id:
            return []
        #client.save_step_output({"dataset_id": dataset_id}, "01_dataset", output_dir)
        
        # Step 2: Check if document exists, upload if needed
        doc_id = client.check_document_exists(dataset_id, filename)
        if not doc_id:
            doc_id = client.upload_document(dataset_id, pdf_path)
            if not doc_id:
                return []
        else:
            print(f"   ♻️ Using existing document: {doc_id}")
        #client.save_step_output({"document_id": doc_id}, "02_upload", output_dir)
        
        # Step 3: Parse document
        if not client.parse_documents(dataset_id, doc_id):
            return []
        #client.save_step_output({"parse_initiated": True}, "03_parse", output_dir)
        
        # Step 4: Wait for completion
        success, doc_info = client.wait_for_parsing_completion(dataset_id, doc_id)
        #client.save_step_output(doc_info, "04_status", output_dir)
        if not success:
            return []
        
        # Step 5: Retrieve chunks
        chunks = client.retrieve_chunks(dataset_id, doc_id)
        #client.save_step_output(chunks, "05_chunks", output_dir)
        
        # Format and save readable chunks
        readable_chunks = []
        for i, chunk in enumerate(chunks):
            readable_chunks.append({
                "chunk_id": i+1,
                "content": chunk.get("content", ""),
                "page": chunk.get("position_info", {}).get("page", 0),
                "keywords": chunk.get("important_keywords", [])
            })
        
        #client.save_step_output(readable_chunks, "06_readable_chunks", output_dir)
        
        # Save processing summary
        summary = {
            "pdf_file": filename,
            "dataset_id": dataset_id,
            "document_id": doc_id,
            "chunk_count": len(chunks),
            "processing_time": datetime.now().isoformat(),
            "status": "success"
        }
        #client.save_step_output(summary, "07_summary", output_dir)
        
        print(f" Processing completed! Chunks: {len(chunks)}")
        return chunks
        
    except Exception as e:
        print(f"Processing failed: {str(e)}")
        client.save_step_output({"error": str(e)}, "error", output_dir)
        return []

def extract_elasticsearch_data_only(folder_path, output_base, api_base_url, api_key, dataset_name="testrag_manual"):
    """Improved version: Check existing files, batch parsing, extended processing time"""
    print(f" Processing PDFs and extracting Elasticsearch data from: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print("❌ No PDF files found")
        return []

    print(f"📄 Found {len(pdf_files)} PDF files")
    print(f"📦 Using dataset: {dataset_name}")

    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_base, f"{dataset_name}_elasticsearch_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Output directory: {output_dir}")

    # Step 1: Create or find dataset
    print(f"\n1. Creating/finding dataset: {dataset_name}")
    dataset_id = client.create_or_find_dataset(dataset_name)
    if not dataset_id:
        print("❌ Failed to create or find dataset")
        return []

    print(f"   ✅ Dataset ready: {dataset_id}")

    # Step 2: Check existing documents and collect document IDs for processing
    print(f"\n2. Checking existing documents and preparing for batch processing...")
    document_ids = []
    new_uploads = 0
    existing_docs = 0

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")

        try:
            # Check if document already exists
            doc_id = client.check_document_exists(dataset_id, filename)

            if doc_id:
                # Document exists - skip upload, go directly to parsing
                print(f"   ♻️ Document already exists: {doc_id}")
                print(f"   ⏭️ Skipping upload, will include in batch parsing")
                document_ids.append(doc_id)
                existing_docs += 1
            else:
                # Document doesn't exist - upload it
                print(f"   📤 Uploading new document...")
                doc_id = client.upload_document(dataset_id, pdf_path)
                if not doc_id:
                    print(f"   ❌ Upload failed for {filename}")
                    continue
                print(f"   ✅ Uploaded successfully: {doc_id}")
                document_ids.append(doc_id)
                new_uploads += 1

        except Exception as e:
            print(f"   ❌ Error processing {filename}: {str(e)}")

    if not document_ids:
        print("❌ No documents to process")
        return []

    print(f"\n📊 Processing Summary:")
    print(f"   📄 Total documents: {len(document_ids)}")
    print(f"   📤 New uploads: {new_uploads}")
    print(f"   ♻️ Existing documents: {existing_docs}")

    # Step 3: Batch parse all documents at once
    print(f"\n3. 🚀 Batch parsing {len(document_ids)} documents...")
    print(f"   📋 Document IDs: {document_ids[:3]}{'...' if len(document_ids) > 3 else ''}")

    if not client.parse_documents(dataset_id, document_ids):
        print("❌ Batch parsing failed")
        return []

    print(f"   ✅ Batch parsing initiated for {len(document_ids)} documents")

    # Step 4: Extended wait time for complete processing pipeline
    print(f"\n4. ⏳ Waiting for complete processing pipeline...")
    print(f"   🔄 Pipeline: OCR → Layout Analysis → Tokenization → Embedding → Chunking")
    print(f"   ⏰ Extended wait time: 10 minutes (600 seconds)")

    # Wait longer for the complete processing pipeline
    wait_time = 600  # 10 minutes for multiple documents with full pipeline
    interval = 20    # Check every 30 seconds

    for elapsed in range(0, wait_time, interval):
        print(f"   ⏳ Processing... {elapsed//60}:{elapsed%60:02d} / {wait_time//60}:{wait_time%60:02d}")

        # Check document status periodically
        if elapsed > 0 and elapsed % 120 == 0:  # Check every 2 minutes
            try:
                url = f"{client.api_base_url}/api/v1/datasets/{dataset_id}/documents"
                response = client.session.get(url)
                if response.status_code == 200:
                    docs_result = response.json()
                    if docs_result.get("code") == 0:
                        docs_data = docs_result.get("data", {})
                        docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                        completed = 0
                        processing = 0
                        failed = 0
                        for doc in docs_list:
                            status = doc.get('run', 'unknown')
                            if status in ['FINISH', 'DONE']:  # Both FINISH and DONE mean completed
                                completed += 1
                            elif status in ['RUNNING', 'WAITING']:
                                processing += 1
                            elif status in ['FAIL', 'ERROR']:
                                failed += 1

                        print(f"   📊 Status check: {completed} completed, {processing} processing, {failed} failed, {len(docs_list)} total")

                        # If all documents are completed or failed (no more processing), break early
                        if processing == 0:
                            if completed > 0:
                                print(f"   🎉 All documents completed processing! ({completed} successful, {failed} failed)")
                            else:
                                print(f"   ⚠️  No documents completed successfully ({failed} failed)")
                            break
            except Exception as e:
                print(f"   ⚠️ Status check failed: {str(e)}")

        time.sleep(interval)

    # Step 5: Final document status check and chunk extraction
    print(f"\n5. 📋 Final document status check...")
    try:
        url = f"{client.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        response = client.session.get(url)
        if response.status_code == 200:
            docs_result = response.json()
            if docs_result.get("code") == 0:
                docs_data = docs_result.get("data", {})
                docs_list = docs_data.get("docs", []) if isinstance(docs_data, dict) else docs_data

                completed_docs = 0
                total_chunks = 0

                print(f"   📊 Final Status Report:")
                for doc in docs_list:
                    status = doc.get('run', 'Unknown')
                    progress = doc.get('progress', 0)
                    chunk_count = doc.get('chunk_count', 0)
                    progress_msg = doc.get('progress_msg', '')
                    name = doc.get('name', 'Unknown')

                    print(f"      📄 {name}: Status={status}, Progress={progress:.1%}, Chunks={chunk_count}")

                    if status == 'FINISH':
                        completed_docs += 1
                        total_chunks += chunk_count
                    elif progress_msg and 'ERROR' in progress_msg:
                        print(f"         ❌ Error: {progress_msg}")

                print(f"   ✅ Completed: {completed_docs}/{len(docs_list)} documents")
                print(f"   🧩 Total chunks generated: {total_chunks}")

    except Exception as e:
        print(f"   ⚠️ Final status check failed: {str(e)}")

    # Step 6: Extract chunks using optimized retrieval
    print(f"\n6. 📥 Extracting chunks from processed documents...")

    # Try multiple retrieval strategies for best results
    all_chunks = []

    # Strategy 1: Get all chunks from dataset (most comprehensive)
    print("   🔍 Strategy 1: Retrieving all chunks from dataset...")
    all_chunks = client.retrieve_chunks(dataset_id)

    if all_chunks:
        print(f"   ✅ Retrieved {len(all_chunks)} chunks from dataset")
    else:
        # Strategy 2: Try with document filter
        print("   🔍 Strategy 2: Retrieving with document filter...")
        all_chunks = client.retrieve_chunks(dataset_id, document_ids)

        if all_chunks:
            print(f"   ✅ Retrieved {len(all_chunks)} chunks with document filter")
        else:
            # Strategy 3: Alternative retrieval method
            print("   🔍 Strategy 3: Alternative retrieval method...")
            try:
                url = f"{client.api_base_url}/api/v1/retrieval"
                payload = {
                    "question": "*",  # Wildcard to get all content
                    "dataset_ids": [dataset_id],
                    "page": 1,
                    "page_size": 1000,
                    "similarity_threshold": 0.0,
                    "keyword": False,
                    "highlight": False
                }
                response = client.session.post(url, json=payload)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        chunks = result.get("data", {}).get("chunks", [])
                        if chunks:
                            all_chunks = chunks
                            print(f"   ✅ Retrieved {len(chunks)} chunks with alternative method")
                        else:
                            print("   ⚠️ Alternative method returned no chunks")
                    else:
                        print(f"   ❌ Alternative method failed: {result.get('message')}")
                else:
                    print(f"   ❌ Alternative method HTTP error: {response.status_code}")
            except Exception as e:
                print(f"   ❌ Alternative method exception: {str(e)}")

    # Step 7: Save final results
    print(f"\n7. 💾 Saving final Elasticsearch data...")

    if all_chunks:
        output_file = os.path.join(output_dir, f"{dataset_name}_elasticsearch_data.json")

        # Create comprehensive output data
        output_data = {
            "dataset_name": dataset_name,
            "dataset_id": dataset_id,
            "processing_summary": {
                "total_pdf_files": len(pdf_files),
                "new_uploads": new_uploads,
                "existing_documents": existing_docs,
                "total_documents_processed": len(document_ids),
                "total_chunks_extracted": len(all_chunks),
                "processing_time_minutes": wait_time // 60,
                "extraction_timestamp": datetime.now().isoformat()
            },
            "document_ids": document_ids,
            "chunks": all_chunks
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"   ✅ Data saved to: {output_file}")

        # Print final summary
        print(f"\n🎉 Processing Complete!")
        print(f"📊 Final Summary:")
        print(f"   📦 Dataset: {dataset_name}")
        print(f"   📄 PDF files processed: {len(pdf_files)}")
        print(f"   📤 New uploads: {new_uploads}")
        print(f"   ♻️ Existing documents: {existing_docs}")
        print(f"   🧩 Total chunks extracted: {len(all_chunks)}")
        print(f"   ⏰ Total processing time: {wait_time // 60} minutes")
        print(f"   💾 Output file: {output_file}")

        return all_chunks
    else:
        print("❌ No chunks were extracted from any documents")
        return []

def process_pdf_folder_single_dataset(folder_path, output_base, api_base_url, api_key, dataset_name="testrag_naive"):
    """Process all PDFs in a folder using a single shared dataset"""
    print(f"🚀 Starting batch processing of PDFs in: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print(" No PDF files found")
        return []

    print(f"📄 Found {len(pdf_files)} PDF files")
    print(f"📦 Using single dataset: {dataset_name}")

    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create shared output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_base, f"{dataset_name}_batch_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f" Batch output directory: {output_dir}")

    # Step 1: Create single dataset for all PDFs
    print(f"\n1. Creating shared dataset: {dataset_name}")
    dataset_id = client.create_or_find_dataset(dataset_name)
    if not dataset_id:
        print("❌ Failed to create or find dataset")
        return []

    client.save_step_output({"dataset_id": dataset_id, "dataset_name": dataset_name}, "01_dataset", output_dir)
    print(f"   Shared dataset created: {dataset_id}")

    # Step 2: Process each PDF
    results = []
    all_chunks = []
    document_ids = []

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")

        try:
            # Check if document already exists
            doc_id = client.check_document_exists(dataset_id, filename)

            if not doc_id:
                # Upload document to shared dataset
                doc_id = client.upload_document(dataset_id, pdf_path)
                if not doc_id:
                    results.append({"file": filename, "status": "upload_failed", "chunks_count": 0})
                    continue
                print(f"  ✅ Document uploaded: {doc_id}")
            else:
                print(f"  ♻️ Using existing document: {doc_id}")

            document_ids.append({"file": filename, "document_id": doc_id})

            # Parse document (always parse, even if document exists)
            print(f"  🔄 Starting parsing for: {filename}")
            if not client.parse_documents(dataset_id, doc_id):
                results.append({"file": filename, "status": "parse_failed", "chunks_count": 0})
                continue

            # Wait for completion (simplified approach)
            print(f"  ⏳ Waiting for processing to complete...")
            time.sleep(60)  # Wait 60 seconds for processing

            # Retrieve chunks
            chunks = client.retrieve_chunks(dataset_id, doc_id)
            all_chunks.extend(chunks)

            # Store chunks in Elasticsearch
            if chunks:
                es_success, es_count = client.store_in_elasticsearch(chunks, dataset_name)
                if es_success:
                    print(f"  ✅ Stored {es_count} chunks in Elasticsearch")
                else:
                    print(f"  ⚠️ Elasticsearch storage failed, but chunks retrieved successfully")

            results.append({
                "file": filename,
                "document_id": doc_id,
                "chunks_count": len(chunks),
                "status": "success"
            })

            print(f"   {filename} processed successfully! Chunks: {len(chunks)}")

        except Exception as e:
            print(f"   Failed to process {filename}: {str(e)}")
            results.append({"file": filename, "status": "error", "chunks_count": 0, "error": str(e)})

    # Save all results
    client.save_step_output(document_ids, "02_document_ids", output_dir)
    client.save_step_output(all_chunks, "03_all_chunks", output_dir)

    # Format and save readable chunks
    readable_chunks = []
    for i, chunk in enumerate(all_chunks):
        readable_chunks.append({
            "chunk_id": i+1,
            "content": chunk.get("content", ""),
            "page": chunk.get("position_info", {}).get("page", 0),
            "keywords": chunk.get("important_keywords", []),
            "document_name": chunk.get("document_name", "unknown")
        })

    client.save_step_output(readable_chunks, "04_readable_chunks", output_dir)

    # Save processing summary
    summary = {
        "dataset_name": dataset_name,
        "dataset_id": dataset_id,
        "total_files": len(pdf_files),
        "processed": len([r for r in results if r["status"] == "success"]),
        "failed": len([r for r in results if r["status"] != "success"]),
        "total_chunks": len(all_chunks),
        "processing_time": datetime.now().isoformat(),
        "results": results
    }
    client.save_step_output(summary, "05_summary", output_dir)

    print(f"\n Batch processing complete!")
    print(f" Dataset: {dataset_name} (ID: {dataset_id})")
    print(f" Files processed: {summary['processed']}/{summary['total_files']}")
    print(f" Total chunks: {summary['total_chunks']}")
    print(f" Results saved to: {output_dir}")

    return results

def process_pdf_folder(folder_path, dataset_name, output_base, api_base_url, api_key):
    """Process all PDFs in a folder using a single user-provided dataset name"""
    print(f"🚀 Starting batch processing of PDFs in: {folder_path}")
    pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))

    if not pdf_files:
        print(" No PDF files found")
        return []

    print(f"Found {len(pdf_files)} PDF files")
    results = []

    for i, pdf_path in enumerate(pdf_files, 1):
        filename = os.path.basename(pdf_path)
        print(f"\n🔹 Processing {i}/{len(pdf_files)}: {filename}")
        chunks = process_pdf(pdf_path, dataset_name, output_base, api_base_url, api_key)
        results.append({
            "file": filename,
            "chunks_count": len(chunks),
            "status": "success" if chunks else "failed"
        })

    # Save batch summary
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = os.path.join(output_base, f"batch_summary_{timestamp}.json")
    with open(summary_path, 'w') as f:
        json.dump({
            "total_files": len(pdf_files),
            "processed": len([r for r in results if r["status"] == "success"]),
            "failed": len([r for r in results if r["status"] == "failed"]),
            "results": results
        }, f, indent=2)

    print(f"\n🎉 Batch processing complete! Summary saved to: {summary_path}")
    return results

def main():
    # Configuration - USE ENVIRONMENT VARIABLES IN PRODUCTION!
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = os.getenv("RAGFLOW_API_KEY", "ragflow-BjMmJmYjBlNTBlMDExZjBiYjYyMDI0Mj")

    # Path configuration (replace with your paths)
    PDF_FOLDER = r"/ml/shared/lazhang/data/pdfdata"
    OUTPUT_BASE = r"/ml/shared/lazhang/data"

    # User-provided dataset name - NO AUTO-GENERATION
    DATASET_NAME = "user_documents"  # Change this to your preferred dataset name

    # Extract Elasticsearch data with all improvements
    chunks = extract_elasticsearch_data_only(PDF_FOLDER, OUTPUT_BASE, API_BASE_URL, API_KEY, DATASET_NAME)

    if chunks:
        print(f"\n🎉 SUCCESS: Extracted {len(chunks)} chunks from RAGFlow!")
        print("📄 Sample chunk content (first 200 characters):")
        if len(chunks) > 0 and chunks[0].get('content'):
            sample_content = chunks[0]['content'][:200]
            print(f"   '{sample_content}{'...' if len(chunks[0]['content']) > 200 else ''}'")
    else:
        print("\n❌ No chunks were extracted. Check the logs above for issues.")

if __name__ == "__main__":
    main()