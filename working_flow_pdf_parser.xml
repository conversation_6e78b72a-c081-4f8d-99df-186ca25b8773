<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 602.90625 3180" style="max-width: 602.90625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df"><style>#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .error-icon{fill:#a44141;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .error-text{fill:#ddd;stroke:#ddd;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-thickness-normal{stroke-width:1px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-thickness-thick{stroke-width:3.5px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-pattern-solid{stroke-dasharray:0;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .marker.cross{stroke:lightgrey;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df p{margin:0;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster-label text{fill:#F9FFFE;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster-label span{color:#F9FFFE;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster-label span p{background-color:transparent;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .label text,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df span{fill:#ccc;color:#ccc;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node rect,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node circle,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node ellipse,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node polygon,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .rough-node .label text,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node .label text,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .image-shape .label,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .icon-shape .label{text-anchor:middle;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .rough-node .label,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node .label,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .image-shape .label,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .icon-shape .label{text-align:center;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .node.clickable{cursor:pointer;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .arrowheadPath{fill:lightgrey;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster text{fill:#F9FFFE;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .cluster span{color:#F9FFFE;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df rect.text{fill:none;stroke-width:0;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .icon-shape,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .icon-shape p,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .icon-shape rect,#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph2" class="cluster"><rect height="520" width="306.6953125" y="2548" x="19.65234375" style=""></rect><g transform="translate(117.25, 2548)" class="cluster-label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Extraction</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="1688" width="330" y="706" x="8" style=""></rect><g transform="translate(84.7265625, 706)" class="cluster-label"><foreignObject height="24" width="176.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAGFlow Internal Engine</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="416" width="320.53125" y="240" x="12.734375" style=""></rect><g transform="translate(103.55859375, 240)" class="cluster-label"><foreignObject height="24" width="138.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAGFlow SDK Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M173,86L173,90.167C173,94.333,173,102.667,173,110.333C173,118,173,125,173,128.5L173,132"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M173,190L173,194.167C173,198.333,173,206.667,173,215C173,223.333,173,231.667,173,239.333C173,247,173,254,173,257.5L173,261"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M173,319L173,323.167C173,327.333,173,335.667,209.182,345.93C245.364,356.194,317.727,368.388,353.909,374.485L390.091,380.582"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M481.586,423L481.586,427.167C481.586,431.333,481.586,439.667,481.586,447.333C481.586,455,481.586,462,481.586,465.5L481.586,469"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M368.266,519.096L335.721,524.58C303.177,530.064,238.089,541.032,205.544,550.016C173,559,173,566,173,569.5L173,573"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M173,631L173,635.167C173,639.333,173,647.667,173,656C173,664.333,173,672.667,173,681C173,689.333,173,697.667,173,705.333C173,713,173,720,173,723.5L173,727"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M173,809L173,813.167C173,817.333,173,825.667,173,833.333C173,841,173,848,173,851.5L173,855"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M173,913L173,917.167C173,921.333,173,929.667,173,937.333C173,945,173,952,173,955.5L173,959"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M173,1017L173,1021.167C173,1025.333,173,1033.667,173,1041.333C173,1049,173,1056,173,1059.5L173,1063"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M173,1121L173,1125.167C173,1129.333,173,1137.667,173,1145.333C173,1153,173,1160,173,1163.5L173,1167"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M173,1225L173,1229.167C173,1233.333,173,1241.667,173,1249.333C173,1257,173,1264,173,1267.5L173,1271"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M173,1329L173,1333.167C173,1337.333,173,1345.667,173,1353.333C173,1361,173,1368,173,1371.5L173,1375"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M173,1433L173,1437.167C173,1441.333,173,1449.667,173,1457.333C173,1465,173,1472,173,1475.5L173,1479"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M173,1537L173,1541.167C173,1545.333,173,1553.667,173,1561.333C173,1569,173,1576,173,1579.5L173,1583"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M173,1641L173,1645.167C173,1649.333,173,1657.667,173,1665.333C173,1673,173,1680,173,1683.5L173,1687"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M173,1745L173,1749.167C173,1753.333,173,1761.667,173,1769.333C173,1777,173,1784,173,1787.5L173,1791"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M173,1849L173,1853.167C173,1857.333,173,1865.667,173,1873.333C173,1881,173,1888,173,1891.5L173,1895"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M173,1953L173,1957.167C173,1961.333,173,1969.667,173,1977.333C173,1985,173,1992,173,1995.5L173,1999"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_18" d="M173,2057L173,2061.167C173,2065.333,173,2073.667,173,2081.333C173,2089,173,2096,173,2099.5L173,2103"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_19" d="M173,2161L173,2165.167C173,2169.333,173,2177.667,173,2185.333C173,2193,173,2200,173,2203.5L173,2207"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_20" d="M173,2265L173,2269.167C173,2273.333,173,2281.667,173,2289.333C173,2297,173,2304,173,2307.5L173,2311"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_21" d="M173,2369L173,2373.167C173,2377.333,173,2385.667,173,2394C173,2402.333,173,2410.667,173,2418.333C173,2426,173,2433,173,2436.5L173,2440"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_22" d="M173,2498L173,2502.167C173,2506.333,173,2514.667,173,2523C173,2531.333,173,2539.667,173,2547.333C173,2555,173,2562,173,2565.5L173,2569"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_23" d="M173,2627L173,2631.167C173,2635.333,173,2643.667,173,2651.333C173,2659,173,2666,173,2669.5L173,2673"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_24" d="M173,2731L173,2735.167C173,2739.333,173,2747.667,173,2755.333C173,2763,173,2770,173,2773.5L173,2777"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_25" d="M173,2835L173,2839.167C173,2843.333,173,2851.667,173,2859.333C173,2867,173,2874,173,2877.5L173,2881"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_26" d="M173,2939L173,2943.167C173,2947.333,173,2955.667,173,2963.333C173,2971,173,2978,173,2981.5L173,2985"></path><path marker-end="url(#mermaid-5c83d7d6-a1a6-4d05-82d7-f8abed00d9df_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_27" d="M173,3043L173,3047.167C173,3051.333,173,3059.667,173,3068C173,3076.333,173,3084.667,173,3092.333C173,3100,173,3107,173,3110.5L173,3114"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(173, 47)" id="flowchart-A-3651" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>python main.py /path/to/pdfs/</p></span></div></foreignObject></g></g><g transform="translate(173, 163)" id="flowchart-B-3652" class="node default"><rect height="54" width="215.90625" y="-27" x="-107.953125" style="" class="basic label-container"></rect><g transform="translate(-77.953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="155.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Setup RAGFlow Client</p></span></div></foreignObject></g></g><g transform="translate(173, 292)" id="flowchart-C-3654" class="node default"><rect height="54" width="227.359375" y="-27" x="-113.6796875" style="" class="basic label-container"></rect><g transform="translate(-83.6796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Knowledge Base</p></span></div></foreignObject></g></g><g transform="translate(481.5859375, 396)" id="flowchart-D-3656" class="node default"><rect height="54" width="175.1015625" y="-27" x="-87.55078125" style="" class="basic label-container"></rect><g transform="translate(-57.55078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.1015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Scan PDF Folder</p></span></div></foreignObject></g></g><g transform="translate(481.5859375, 500)" id="flowchart-E-3658" class="node default"><rect height="54" width="226.640625" y="-27" x="-113.3203125" style="" class="basic label-container"></rect><g transform="translate(-83.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="166.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Read PDF Files as Blobs</p></span></div></foreignObject></g></g><g transform="translate(173, 604)" id="flowchart-F-3660" class="node default"><rect height="54" width="250.53125" y="-27" x="-125.265625" style="" class="basic label-container"></rect><g transform="translate(-95.265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="190.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Upload Documents via SDK</p></span></div></foreignObject></g></g><g transform="translate(173, 770)" id="flowchart-G-3662" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAGFlow Internal Processing</p></span></div></foreignObject></g></g><g transform="translate(173, 886)" id="flowchart-H-3664" class="node default"><rect height="54" width="242.0390625" y="-27" x="-121.01953125" style="" class="basic label-container"></rect><g transform="translate(-91.01953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="182.0390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Document Records in SQL</p></span></div></foreignObject></g></g><g transform="translate(173, 990)" id="flowchart-I-3666" class="node default"><rect height="54" width="206.796875" y="-27" x="-103.3984375" style="" class="basic label-container"></rect><g transform="translate(-73.3984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Queue Creation</p></span></div></foreignObject></g></g><g transform="translate(173, 1094)" id="flowchart-J-3668" class="node default"><rect height="54" width="245.4140625" y="-27" x="-122.70703125" style="" class="basic label-container"></rect><g transform="translate(-92.70703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.4140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Background Task Executor</p></span></div></foreignObject></g></g><g transform="translate(173, 1198)" id="flowchart-K-3670" class="node default"><rect height="54" width="136.171875" y="-27" x="-68.0859375" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-38.0859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="76.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PDF Parser</p></span></div></foreignObject></g></g><g transform="translate(173, 1302)" id="flowchart-L-3672" class="node default"><rect height="54" width="246.9609375" y="-27" x="-123.48046875" style="" class="basic label-container"></rect><g transform="translate(-93.48046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="186.9609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Layout Recognition &amp; OCR</p></span></div></foreignObject></g></g><g transform="translate(173, 1406)" id="flowchart-M-3674" class="node default"><rect height="54" width="234.1015625" y="-27" x="-117.05078125" style="" class="basic label-container"></rect><g transform="translate(-87.05078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="174.1015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Text Extraction &amp; Tables</p></span></div></foreignObject></g></g><g transform="translate(173, 1510)" id="flowchart-N-3676" class="node default"><rect height="54" width="219.6484375" y="-27" x="-109.82421875" style="" class="basic label-container"></rect><g transform="translate(-79.82421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="159.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Content Preprocessing</p></span></div></foreignObject></g></g><g transform="translate(173, 1614)" id="flowchart-O-3678" class="node default"><rect height="54" width="189.6796875" y="-27" x="-94.83984375" style="" class="basic label-container"></rect><g transform="translate(-64.83984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.6796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chunking Strategy</p></span></div></foreignObject></g></g><g transform="translate(173, 1718)" id="flowchart-P-3680" class="node default"><rect height="54" width="169.84375" y="-27" x="-84.921875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-54.921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chunk Creation</p></span></div></foreignObject></g></g><g transform="translate(173, 1822)" id="flowchart-Q-3682" class="node default"><rect height="54" width="150.21875" y="-27" x="-75.109375" style="" class="basic label-container"></rect><g transform="translate(-45.109375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Tokenization</p></span></div></foreignObject></g></g><g transform="translate(173, 1926)" id="flowchart-R-3684" class="node default"><rect height="54" width="199.484375" y="-27" x="-99.7421875" style="" class="basic label-container"></rect><g transform="translate(-69.7421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="139.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Keyword Extraction</p></span></div></foreignObject></g></g><g transform="translate(173, 2030)" id="flowchart-S-3686" class="node default"><rect height="54" width="179.7734375" y="-27" x="-89.88671875" style="" class="basic label-container"></rect><g transform="translate(-59.88671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.7734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Position Mapping</p></span></div></foreignObject></g></g><g transform="translate(173, 2134)" id="flowchart-T-3688" class="node default"><rect height="54" width="223.3515625" y="-27" x="-111.67578125" style="" class="basic label-container"></rect><g transform="translate(-81.67578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="163.3515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Embedding Generation</p></span></div></foreignObject></g></g><g transform="translate(173, 2238)" id="flowchart-U-3690" class="node default"><rect height="54" width="215.15625" y="-27" x="-107.578125" style="" class="basic label-container"></rect><g transform="translate(-77.578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="155.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SQL Database Storage</p></span></div></foreignObject></g></g><g transform="translate(173, 2342)" id="flowchart-V-3692" class="node default"><rect height="54" width="218.5" y="-27" x="-109.25" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-79.25, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="158.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch Indexing</p></span></div></foreignObject></g></g><g transform="translate(173, 2471)" id="flowchart-W-3694" class="node default"><rect height="54" width="184.296875" y="-27" x="-92.1484375" style="" class="basic label-container"></rect><g transform="translate(-62.1484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Parsing Complete</p></span></div></foreignObject></g></g><g transform="translate(173, 2600)" id="flowchart-X-3696" class="node default"><rect height="54" width="200.703125" y="-27" x="-100.3515625" style="" class="basic label-container"></rect><g transform="translate(-70.3515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="140.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Extract Chunks Tool</p></span></div></foreignObject></g></g><g transform="translate(173, 2704)" id="flowchart-Y-3698" class="node default"><rect height="54" width="236.6953125" y="-27" x="-118.34765625" style="" class="basic label-container"></rect><g transform="translate(-88.34765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176.6953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Connect to Elasticsearch</p></span></div></foreignObject></g></g><g transform="translate(173, 2808)" id="flowchart-Z-3700" class="node default"><rect height="54" width="214.0625" y="-27" x="-107.03125" style="" class="basic label-container"></rect><g transform="translate(-77.03125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="154.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query RAGFlow Index</p></span></div></foreignObject></g></g><g transform="translate(173, 2912)" id="flowchart-AA-3702" class="node default"><rect height="54" width="197.78125" y="-27" x="-98.890625" style="" class="basic label-container"></rect><g transform="translate(-68.890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="137.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Retrieve All Chunks</p></span></div></foreignObject></g></g><g transform="translate(173, 3016)" id="flowchart-BB-3704" class="node default"><rect height="54" width="231.0546875" y="-27" x="-115.52734375" style="" class="basic label-container"></rect><g transform="translate(-85.52734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="171.0546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Save JSON &amp; HTML Files</p></span></div></foreignObject></g></g><g transform="translate(173, 3145)" id="flowchart-CC-3706" class="node default"><rect height="54" width="190.8125" y="-27" x="-95.40625" style="fill:#c8e6c9 !important" class="basic label-container"></rect><g transform="translate(-65.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="130.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pipeline Complete</p></span></div></foreignObject></g></g></g></g></g></svg>