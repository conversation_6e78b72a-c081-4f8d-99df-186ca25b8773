#!/usr/bin/env python3
"""
Modified RAGFlow processing script that forces DiT usage
This bypasses the configuration issue by directly specifying DiT
"""

import requests
import json
import time
import os

def process_with_dit_config():
    """Process PDFs with DiT configuration forced"""
    
    api_base_url = "http://127.0.0.1:9380"
    api_key = "ragflow-IwODI3YmEzNzk2NzExZWZiMGEzMDI0MmFjMTMwMDA2"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Create a new dataset with DiT configuration
    dataset_payload = {
        "name": "table_test_dit",
        "description": "Testing table processing with DiT model",
        "language": "English",
        "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI",
        "chunk_method": "naive",
        "parser_config": {
            "chunk_token_num": 128,
            "delimiter": "\n",
            "html4excel": False,
            "layout_recognize": "DiT",  # Force DiT usage
            "raptor": {"use_raptor": False},
            "filename_embd_weight": 0.1  # Prevent embedding errors
        }
    }
    
    try:
        # Create dataset
        create_url = f"{api_base_url}/api/v1/datasets"
        response = requests.post(create_url, headers=headers, json=dataset_payload)
        
        if response.status_code == 200:
            dataset_data = response.json()
            dataset_id = dataset_data.get('data', {}).get('dataset_id')
            print(f"✅ Created DiT dataset: {dataset_id}")
            
            # Upload test PDF
            pdf_path = "/ml/shared/lazhang/data/pdfdata/table_test_obvious.pdf"
            if os.path.exists(pdf_path):
                print(f"📤 Uploading: {pdf_path}")
                
                # Upload file
                with open(pdf_path, 'rb') as f:
                    files = {'file': ('table_test_obvious.pdf', f, 'application/pdf')}
                    upload_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/documents"
                    
                    upload_response = requests.post(upload_url, headers={'Authorization': f'Bearer {api_key}'}, files=files)
                    
                    if upload_response.status_code == 200:
                        doc_data = upload_response.json()
                        doc_id = doc_data.get('data', {}).get('document_id')
                        print(f"✅ Uploaded document: {doc_id}")
                        
                        # Parse with DiT
                        parse_payload = {
                            "document_ids": [doc_id]
                        }
                        
                        parse_url = f"{api_base_url}/api/v1/datasets/{dataset_id}/chunks"
                        parse_response = requests.post(parse_url, headers=headers, json=parse_payload)
                        
                        if parse_response.status_code == 200:
                            print(f"✅ Parsing initiated with DiT configuration")
                            print(f"🔄 Monitor Docker logs for table processing...")
                            return True
                        else:
                            print(f"❌ Parse failed: {parse_response.status_code}")
                    else:
                        print(f"❌ Upload failed: {upload_response.status_code}")
            else:
                print(f"❌ Test PDF not found: {pdf_path}")
        else:
            print(f"❌ Dataset creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    return False

if __name__ == "__main__":
    print("🚀 PROCESSING WITH DiT CONFIGURATION")
    print("="*50)
    process_with_dit_config()
