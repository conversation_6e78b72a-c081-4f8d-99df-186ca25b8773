#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import hashlib
import re
from datetime import datetime
from uuid import uuid4
import copy

# Try to import PDF processing libraries
try:
    import pdfplumber
    PDF_LIBRARY = "pdfplumber"
except ImportError:
    try:
        import PyPDF2
        PDF_LIBRARY = "PyPDF2"
    except ImportError:
        print("❌ No PDF processing library available. Please install pdfplumber or PyPDF2")
        sys.exit(1)

def get_uuid():
    """Generate UUID for documents and chunks"""
    return str(uuid4()).replace("-", "")

def create_output_directory(base_output_path, pdf_filename):
    """Create timestamped output directory for saving step outputs"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    pdf_name = os.path.splitext(pdf_filename)[0]
    output_dir = os.path.join(base_output_path, f"{pdf_name}_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_step_output(data, step_name, output_dir, filename_prefix=""):
    """Save output from each processing step"""
    try:
        if filename_prefix:
            filename = f"{filename_prefix}_{step_name}.json"
        else:
            filename = f"{step_name}.json"

        filepath = os.path.join(output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)

        print(f"  💾 Saved {step_name} output to: {filepath}")
        return filepath
    except Exception as e:
        print(f"  ⚠️  Failed to save {step_name} output: {e}")
        return None

def simple_tokenize(text):
    """Simple tokenization that mimics RAGFlow's tokenizer behavior"""
    # Basic text cleaning
    text = re.sub(r'\W+', ' ', text)
    text = text.lower().strip()
    return text

def fine_grained_tokenize(text):
    """Simple fine-grained tokenization"""
    tokens = text.split()
    # Split tokens further by common delimiters
    fine_tokens = []
    for token in tokens:
        if len(token) > 3:
            # Split by common patterns
            parts = re.split(r'[/_-]', token)
            fine_tokens.extend(parts)
        else:
            fine_tokens.append(token)
    return ' '.join(fine_tokens)

def num_tokens_from_string(text):
    """Estimate token count"""
    return len(text.split())

def parse_pdf_ragflow_format(pdf_path, output_dir=None):
    """Parse PDF using RAGFlow format structure"""
    print(f"📄 Step 1: Parsing PDF: {os.path.basename(pdf_path)}")
    
    try:
        sections = []
        tables = []
        
        if PDF_LIBRARY == "pdfplumber":
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text - RAGFlow format: (text, image_info)
                    text = page.extract_text()
                    if text:
                        sections.append((text, ""))  # RAGFlow format: tuple (text, image)
                    
                    # Extract tables
                    page_tables = page.extract_tables()
                    for table in page_tables:
                        if table:
                            tables.append(table)
        
        elif PDF_LIBRARY == "PyPDF2":
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    text = page.extract_text()
                    if text:
                        sections.append((text, ""))  # RAGFlow format
        
        print(f"  ✅ Extracted {len(sections)} sections and {len(tables)} tables")
        
        # Save parsing output in RAGFlow format
        if output_dir:
            parsing_output = {
                "pdf_path": pdf_path,
                "pdf_library": PDF_LIBRARY,
                "sections_count": len(sections),
                "tables_count": len(tables),
                "sections": [{"text": section[0], "image": section[1]} for section in sections[:10]],  # Sample
                "tables": tables[:5]  # Sample tables
            }
            save_step_output(parsing_output, "01_pdf_parsing", output_dir)
        
        return sections, tables
    
    except Exception as e:
        print(f"  ❌ Failed to parse PDF: {e}")
        return [], []

def naive_merge_ragflow_format(sections, chunk_token_num=512, delimiter="\n。；！？", output_dir=None, pdf_filename="document.pdf"):
    """Chunk content using RAGFlow's naive merge format"""
    print(f"✂️  Step 2: Content Chunking (naive merge)")

    try:
        # Create document base metadata (RAGFlow format)
        doc_base = {
            "docnm_kwd": os.path.basename(pdf_filename),
            "title_tks": simple_tokenize(os.path.basename(pdf_filename).replace('.pdf', '')),
            "doc_id": get_uuid(),
            "kb_id": get_uuid(),
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "create_timestamp_flt": datetime.now().timestamp()
        }
        doc_base["title_sm_tks"] = fine_grained_tokenize(doc_base["title_tks"])
        
        # Convert sections to text
        full_text = "\n\n".join([section[0] for section in sections])
        
        # Simple chunking by token count (mimicking RAGFlow's naive merge)
        chunks = []
        current_chunk = ""
        current_tokens = 0
        
        # Split by sentences/paragraphs
        sentences = re.split(f'[{delimiter}]', full_text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            sentence_tokens = num_tokens_from_string(sentence)
            
            if current_tokens + sentence_tokens > chunk_token_num and current_chunk:
                # Save current chunk
                chunks.append(current_chunk.strip())
                current_chunk = sentence
                current_tokens = sentence_tokens
            else:
                current_chunk += (" " + sentence if current_chunk else sentence)
                current_tokens += sentence_tokens
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        print(f"  ✅ Created {len(chunks)} chunks")
        
        # Save chunking output in RAGFlow format
        if output_dir:
            chunking_output = {
                "filename": doc_base["docnm_kwd"],
                "chunk_token_num": chunk_token_num,
                "delimiter": delimiter,
                "chunks_count": len(chunks),
                "doc_base": doc_base,
                "chunks": chunks,
                "full_text_length": len(full_text)
            }
            save_step_output(chunking_output, "02_content_chunking", output_dir)
        
        return chunks, doc_base
    
    except Exception as e:
        print(f" Failed to chunk content: {e}")
        return [], {}

def tokenize_chunks_ragflow_format(chunks, doc_base, output_dir=None):
    """Tokenize chunks in exact RAGFlow format"""
    print("🔤 Step 3: Tokenizing chunks (RAGFlow format)")
    
    try:
        tokenized_chunks = []
        
        for i, chunk_text in enumerate(chunks):
            if not chunk_text.strip():
                continue
                
            # Create chunk document (RAGFlow format)
            chunk_doc = copy.deepcopy(doc_base)
            
            # Add RAGFlow-specific fields
            chunk_doc["id"] = hashlib.md5((chunk_text + str(i)).encode()).hexdigest()
            chunk_doc["content_with_weight"] = chunk_text
            
            # RAGFlow tokenization fields
            cleaned_text = re.sub(r"</?(table|td|caption|tr|th)( [^<>]{0,12})?>", " ", chunk_text)
            chunk_doc["content_ltks"] = simple_tokenize(cleaned_text)
            chunk_doc["content_sm_ltks"] = fine_grained_tokenize(chunk_doc["content_ltks"])
            
            # Additional RAGFlow fields
            chunk_doc["available"] = True
            chunk_doc["document_id"] = doc_base["doc_id"]
            chunk_doc["image_id"] = ""
            chunk_doc["important_keywords"] = []
            chunk_doc["positions"] = [[i, 0, 0, 100, 100]]  # Default position
            
            # Optional fields that might be present
            chunk_doc["image"] = None
            chunk_doc["question_kwd"] = []
            chunk_doc["important_kwd"] = []
            chunk_doc["important_tks"] = ""
            chunk_doc["question_tks"] = ""
            
            tokenized_chunks.append(chunk_doc)
        
        print(f"Tokenized {len(tokenized_chunks)} chunks")
        
        # Save tokenization output in RAGFlow format
        if output_dir:
            tokenization_output = {
                "tokenized_chunks_count": len(tokenized_chunks),
                "doc_base": doc_base,
                "sample_chunk_fields": list(tokenized_chunks[0].keys()) if tokenized_chunks else [],
                "tokenized_chunks": tokenized_chunks
            }
            save_step_output(tokenization_output, "03_tokenization", output_dir)
        
        return tokenized_chunks

    except Exception as e:
        print(f"Failed to tokenize chunks: {e}")
        return []

def process_pdf_first_3_steps(pdf_path, output_path, chunk_token_num=512):
    """Process PDF through first 3 steps with RAGFlow format"""
    print(f"📄 Processing PDF: {pdf_path}")
    print(f"📁 Output path: {output_path}")

    if not os.path.exists(pdf_path):
        print(f"PDF file does not exist: {pdf_path}")
        return None

    # Create base output directory
    os.makedirs(output_path, exist_ok=True)

    filename = os.path.basename(pdf_path)

    # Create output directory for this PDF
    pdf_output_dir = create_output_directory(output_path, filename)
    print(f"Step outputs will be saved to: {pdf_output_dir}")

    # Step 1: Parse PDF
    sections, tables = parse_pdf_ragflow_format(pdf_path, pdf_output_dir)
    if not sections:
        print("No sections extracted from PDF")
        return None

    # Step 2: Chunk content using naive merge
    chunks, doc_base = naive_merge_ragflow_format(
        sections,
        chunk_token_num=chunk_token_num,
        output_dir=pdf_output_dir,
        pdf_filename=filename
    )
    if not chunks:
        print("No chunks created")
        return None

    # Step 3: Tokenize chunks
    tokenized_chunks = tokenize_chunks_ragflow_format(chunks, doc_base, pdf_output_dir)
    if not tokenized_chunks:
        print("No tokenized chunks created")
        return None

    print(f"\n First 3 steps completed successfully!")
    print(f"Results:")
    print(f"   • Sections extracted: {len(sections)}")
    print(f"   • Tables extracted: {len(tables)}")
    print(f"   • Chunks created: {len(chunks)}")
    print(f"   • Tokenized chunks: {len(tokenized_chunks)}")
    print(f" All outputs saved to: {pdf_output_dir}")

    return {
        "sections": sections,
        "tables": tables,
        "chunks": chunks,
        "doc_base": doc_base,
        "tokenized_chunks": tokenized_chunks,
        "output_dir": pdf_output_dir
    }

def main():
    """Main function"""
    if len(sys.argv) == 1:
        # Default processing for your specific file
        pdf_path = r"/ml/shared/AI_Lab/g2module_docs/g2mod.pdf"
        output_path = r"/ml/shared/lazhang/data"

        print("Starting RAGFlow First 3 Steps Processing Pipeline")
        print("=" * 60)
        print(f"Processing: {pdf_path}")
        print(f"Output: {output_path}")

        result = process_pdf_first_3_steps(pdf_path, output_path)

    elif len(sys.argv) == 3:
        # Process single PDF with custom output
        pdf_path = sys.argv[1]
        output_path = sys.argv[2]

        print("Starting RAGFlow First 3 Steps Processing Pipeline")
        print("=" * 60)

        result = process_pdf_first_3_steps(pdf_path, output_path)

    elif len(sys.argv) == 4:
        # Process with custom chunk size
        pdf_path = sys.argv[1]
        output_path = sys.argv[2]
        chunk_token_num = int(sys.argv[3])

        print("Starting RAGFlow First 3 Steps Processing Pipeline")
        print("=" * 60)

        result = process_pdf_first_3_steps(pdf_path, output_path, chunk_token_num)

    else:
        print("Usage:")
        print("  python main_first_3_steps.py                                    # Process default PDF")
        print("  python main_first_3_steps.py <pdf_path> <output_path>           # Process single PDF")
        print("  python main_first_3_steps.py <pdf_path> <output_path> <tokens>  # Process with custom chunk size")

if __name__ == "__main__":
    main()
